package com.unipus.digitalbook.model.params;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "long 类型的id列表，用于批量删除，批量激活等场景")
public class IdListParams implements Params{
    @Schema( description = "id列表")
    List<Long> idList;

    public List<Long> getIdList() {
        return idList;
    }

    public IdListParams setIdList(List<Long> idList) {
        this.idList = idList;
        return this;
    }

    /**
     *
     */
    @Override
    public void valid() {
        if (idList == null || idList.isEmpty()) {
            throw new IllegalArgumentException("idList不能为空");
        }
    }
}
