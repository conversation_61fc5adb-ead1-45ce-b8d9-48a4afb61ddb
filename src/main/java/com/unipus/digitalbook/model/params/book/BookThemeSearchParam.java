package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.BookTheme;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;

/**
 * 教材主题实体
 */
@Schema(description = "教材主题实体")
public class BookThemeSearchParam implements Serializable, Params {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "关联教材ID")
    private String bookId;

    @Schema(description = "生效章节ID，‘-’代表全部章节")
    private String chapterId;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public BookTheme toEntity() {
        BookTheme bookTheme = new BookTheme();
        bookTheme.setBookId(bookId);
        bookTheme.setChapterId(chapterId);
        bookTheme.setEnable(true);
        return bookTheme;
    }

    @Override
    public void valid() {
        if(!StringUtils.hasText(bookId) && !StringUtils.hasText(chapterId)){
            throw new IllegalArgumentException("bookId与chapterId不能都为空");
        }
    }
}
