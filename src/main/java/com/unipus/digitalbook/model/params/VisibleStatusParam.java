package com.unipus.digitalbook.model.params;


import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "可见状态参数")
public class VisibleStatusParam<T> implements Params {
    @Schema(description = "id")
    private T id;

    @Schema(description = "状态")
    private Integer visibleStatus;

    public T getId() {
        return id;
    }

    public void setId(T id) {
        this.id = id;
    }

    public Integer getVisibleStatus() {
        return visibleStatus;
    }

    public void setVisibleStatus(Integer visibleStatus) {
        this.visibleStatus = visibleStatus;
    }

    @Override
    public void valid() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (visibleStatus == null) {
            throw new IllegalArgumentException("visibleStatus不能为空");
        }
    }
}
