package com.unipus.digitalbook.model.params.assistant;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.qcloud.cos.utils.Md5Utils;
import com.unipus.digitalbook.model.po.assistant.AssistantPO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "教材添加数字人参数")
public class AssistantAddParam implements Serializable {

    @Schema(description = "助手名称")
    private String name;

    @Schema(description = "配置类型")
    @NotNull
    private Integer configType;

    @Schema(description = "助教类型")
    @NotNull
    private Integer type;

    @Schema(description = "教材id")
    @NotBlank
    private String bookId;

    @Schema(description = "章节id")
    @NotBlank
    private String chapterId;

    @Schema(description = "块ids")
    @NotEmpty
    private List<String> blockIds;

    @Schema(description = "模板id")
    private String templateId;

    @Schema(description = "模板json")
    private String templateJson;

    public AssistantPO toEntity(Long currentUserId) {
        Date now = Calendar.getInstance().getTime();
        AssistantPO assistantPO = new AssistantPO();
        assistantPO.setId(generateId(getBookId(), getChapterId(), getBlockIds()));
        assistantPO.setEnable(true);
        assistantPO.setType(getType());
        assistantPO.setBookId(getBookId());
        assistantPO.setCreateBy(currentUserId);
        assistantPO.setCreateTime(now);
        assistantPO.setName(getName());
        assistantPO.setBlockIds(JSON.toJSONString(getBlockIds()));
        assistantPO.setChapterId(getChapterId());
        assistantPO.setConfigType(getConfigType());
        assistantPO.setTemplateId(getTemplateId());
        assistantPO.setTemplateJson(getTemplateJson());
        assistantPO.setUpdateBy(currentUserId);
        assistantPO.setUpdateTime(now);
        return assistantPO;
    }

    private String generateId(String bookId, String chapterId, List<String> blockIds) {
        String[] ids = Sets.newHashSet(blockIds).toArray(new String[0]);
        Arrays.sort(ids);

        // 将排序后的数组转换为字符串
        String sortedIds = bookId + "-" + chapterId + "-" + String.join("_", ids);
        return Md5Utils.md5Hex(sortedIds);
    }
}
