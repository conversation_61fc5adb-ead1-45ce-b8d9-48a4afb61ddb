package com.unipus.digitalbook.model.params.cos;

import com.unipus.digitalbook.model.enums.MediaTranscodeFormatEnum;
import com.unipus.digitalbook.model.enums.MediaTranscodeTemplateEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 检查音频转码任务参数
 *
 * <AUTHOR>
 * @date 2025/01/10
 */
@Data
@Schema(description = "检查音频转码任务参数")
public class CheckMediaTranscodeJobParam implements Params {

    @Schema(description = "转码模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateName;

    @Schema(description = "文件URL", requiredMode = Schema.RequiredMode.REQUIRED)
    private String url;

    @Schema(description = "文件hash值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hash;

    @Override
    public void valid() {
        if (!StringUtils.hasText(templateName)) {
            throw new IllegalArgumentException("转码模板名称不能为空");
        }
        if (!MediaTranscodeTemplateEnum.isValidTemplateName(templateName) &&
                !MediaTranscodeFormatEnum.isValidTemplateName(templateName)) {
            throw new IllegalArgumentException("不支持的转码模板名称: " + templateName);
        }
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("文件URL不能为空");
        }
        if (!StringUtils.hasText(hash)) {
            throw new IllegalArgumentException("文件hash值不能为空");
        }
    }
}
