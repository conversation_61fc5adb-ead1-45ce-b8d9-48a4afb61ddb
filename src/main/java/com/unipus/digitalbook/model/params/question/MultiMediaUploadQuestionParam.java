package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.MultiMediaUploadQuestion;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 多媒体上传题
 */
public class MultiMediaUploadQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {
        if (!CollectionUtils.isEmpty(getChildren())) {
            if (!StringUtils.hasText(getQuesText())) {
                throw new IllegalArgumentException("题干不能为空");
            }
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        MultiMediaUploadQuestion multiMediaUploadQuestion = new MultiMediaUploadQuestion();
        multiMediaUploadQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return multiMediaUploadQuestion;
    }
}
