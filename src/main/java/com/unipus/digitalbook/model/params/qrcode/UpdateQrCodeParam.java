package com.unipus.digitalbook.model.params.qrcode;

import com.unipus.digitalbook.model.entity.qrcode.QrCode;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

public class UpdateQrCodeParam implements Params {

    @Schema(description = "二维码ID（必填）")
    private Long id;

    @Schema(description = "实体教材位置")
    private String realBookLocation;

    @Schema(description = "教材名称")
    private String bookName;

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "教材内部链接")
    private String bookInnerUrl;

    @Schema(description = "备注")
    private String remarks;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRealBookLocation() {
        return realBookLocation;
    }

    public void setRealBookLocation(String realBookLocation) {
        this.realBookLocation = realBookLocation;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookInnerUrl() {
        return bookInnerUrl;
    }

    public void setBookInnerUrl(String bookInnerUrl) {
        this.bookInnerUrl = bookInnerUrl;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public QrCode toEntity(Long userId) {
        QrCode qrCode = new QrCode();
        qrCode.setId(this.id);
        qrCode.setRealBookLocation(this.realBookLocation);
        qrCode.setBookName(this.bookName);
        qrCode.setBookId(this.bookId);
        qrCode.setBookInnerUrl(this.bookInnerUrl);
        qrCode.setRemarks(this.remarks);
        qrCode.setUpdateBy(userId);
        return qrCode;
    }

    @Override
    public void valid() {
        // 试题教材位置：最长可输入50个字符
        if (realBookLocation != null && realBookLocation.length() > 50) {
            throw new IllegalArgumentException("实体教材位置：最大可输入50个字符");
        }

        // 教材名称：必选单选项，未选择数据时，提示：请选择教材名称
        if (bookName == null || bookName.trim().isEmpty()) {
            throw new IllegalArgumentException("教材名称：请选择教材名称");
        }
        if (bookId == null || bookId.isEmpty()) {
            throw new IllegalArgumentException("bookId不能为空");
        }

        // 教材内部链接：最长可输入500个字符
        if (bookInnerUrl != null && bookInnerUrl.length() > 500) {
            throw new IllegalArgumentException("教材内部链接：最长可输入500个字符");
        }

        // 备注：最长可输入300个字符
        if (remarks != null && remarks.length() > 300) {
            throw new IllegalArgumentException("备注：最长可输入300个字符");
        }
    }

}
