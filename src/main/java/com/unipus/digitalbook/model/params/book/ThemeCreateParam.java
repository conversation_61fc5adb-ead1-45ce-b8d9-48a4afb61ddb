package com.unipus.digitalbook.model.params.book;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unipus.digitalbook.model.entity.book.Theme;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;

/**
 * 教材主题实体
 */
@Schema(description = "教材主题实体")
public class ThemeCreateParam implements Serializable, Params {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主题ID")
    private Long id;

    @Schema(description = "主题名称")
    private String name;

    @Schema(description = "主题内容")
    private String content;

    @Schema(description = "是否有效")
    private Boolean enable;

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    @Override
    public void valid() {
        if (!StringUtils.hasText(name)){
            throw new IllegalArgumentException("主题名称不能为空");
        }
        if (!StringUtils.hasText(content)){
            throw new IllegalArgumentException("主题内容不能为空");
        }
        if(StringUtils.hasText(content)){
            try {
                OBJECT_MAPPER.readTree(content);
            } catch (JsonProcessingException e) {
                throw new IllegalArgumentException("主题内容格式不正确");
            }
        }
    }

    public Theme toEntity() {
        Theme theme = new Theme();
        theme.setId(id);
        theme.setName(name);
        theme.setContent(content);
        theme.setEnable(enable);
        return theme;
    }
}
