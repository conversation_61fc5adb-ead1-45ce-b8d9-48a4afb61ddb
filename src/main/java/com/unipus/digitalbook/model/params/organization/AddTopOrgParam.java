package com.unipus.digitalbook.model.params.organization;

import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "添加一级组织参数")
public class AddTopOrgParam implements Params {
    @Schema(description = "组织名称", example = "一个组织名称")
    String name;
    @Schema(description = "组织类型", example = "1:集团、2:公司、3:部门、4:学校")
    Integer organizationType;
    @Schema(description = "组织状态", example = "0:禁用、1:启用")
    Integer status;


    public void valid() {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("请输入机构名称");
        }
        // name 最大长度为50个字符
        if (name.length() > 50) {
            throw new IllegalArgumentException("组织名称最大可输入50个字符");
        }

        if (organizationType == null) {
            throw new IllegalArgumentException("请选择状态组织类型");
        }
        if (status == null) {
            throw new IllegalArgumentException("请选择状态");
        }
    }


    public String getName() {
        return name;
    }

    public AddTopOrgParam setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getOrganizationType() {
        return organizationType;
    }

    public AddTopOrgParam setOrganizationType(Integer organizationType) {
        this.organizationType = organizationType;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public AddTopOrgParam setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Organization toEntity() {
        Organization organization = new Organization();
        organization.setOrgName(name);
        organization.setOrgType(organizationType);
        organization.setStatus(status);
        return organization;
    }
}
