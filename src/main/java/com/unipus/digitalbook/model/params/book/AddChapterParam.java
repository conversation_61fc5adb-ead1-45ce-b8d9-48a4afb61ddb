package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "增加章节参数")
public class AddChapterParam implements Params {

    @Schema(description = "章节ID")
    String id;

    @Schema(description = "章节名称")
    String name;

    @Schema(description = "所属的教材ID")
    String bookId;

    @Schema(description = "章节排序")
    Integer chapterNumber;

    @Override
    public void valid() {
        ValidationResult result = new ValidationResult();

        if (StringUtils.isBlank(id)) {
            result.addError("id", "章节ID不能为空");
        }

        if (StringUtils.isBlank(name)) {
            result.addError("name", "章节名称不能为空");
        }
        if (name.length()>100){
            result.addError("name", "最大可输入100位");
        }

        if (StringUtils.isBlank(bookId)){
            result.addError("bookId", "教材ID不能为空");
        }

        if (chapterNumber == null) {
            result.addError("sort", "章节排序不能为空");
        } else if (chapterNumber < 0) {
            result.addError("sort", "章节排序不能为负数");
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public Integer getChapterNumber() {
        return chapterNumber;
    }

    public void setChapterNumber(Integer chapterNumber) {
        this.chapterNumber = chapterNumber;
    }


    public Chapter toEntity() {
        Chapter chapter = new Chapter();
        chapter.setId(this.getId());
        chapter.setBookId(this.getBookId());
        chapter.setChapterNumber(this.getChapterNumber());
        chapter.setName(this.getName());
        return chapter;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }
}
