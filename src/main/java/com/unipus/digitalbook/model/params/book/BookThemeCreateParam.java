package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.BookTheme;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;

/**
 * 教材主题实体
 */
@Schema(description = "教材主题创建实体")
public class BookThemeCreateParam implements Serializable, Params {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "关联教材ID")
    private String bookId;

    @Schema(description = "生效章节ID，‘-’代表全部章节")
    private String chapterId;

    @Schema(description = "主题ID")
    private Long themeId;

    @Schema(description = "是否覆盖已编写内容的默认样式，false:否，true:是")
    private Boolean overrideDefault;

    @Schema(description = "是否覆盖已编写内容的自定义样式，false:否，true:是")
    private Boolean overrideCustom;

    @Schema(description = "是否作为生效编辑器的样式模板，false:否，true:是")
    private Boolean editorTemplate;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public Boolean getEditorTemplate() {
        return editorTemplate;
    }

    public void setEditorTemplate(Boolean editorTemplate) {
        this.editorTemplate = editorTemplate;
    }

    public Boolean getOverrideCustom() {
        return overrideCustom;
    }

    public void setOverrideCustom(Boolean overrideCustom) {
        this.overrideCustom = overrideCustom;
    }

    public Boolean getOverrideDefault() {
        return overrideDefault;
    }

    public void setOverrideDefault(Boolean overrideDefault) {
        this.overrideDefault = overrideDefault;
    }

    public Long getThemeId() {
        return themeId;
    }

    public void setThemeId(Long themeId) {
        this.themeId = themeId;
    }

    public BookTheme toEntity() {
        BookTheme bookTheme = new BookTheme();
        bookTheme.setBookId(bookId);
        bookTheme.setChapterId(chapterId);
        bookTheme.setThemeId(themeId);
        bookTheme.setOverrideDefault(overrideDefault);
        bookTheme.setOverrideCustom(overrideCustom);
        bookTheme.setEditorTemplate(editorTemplate);
        bookTheme.setEnable(true);
        return bookTheme;
    }

    @Override
    public void valid() {
        if(!StringUtils.hasText(bookId)){
            throw new IllegalArgumentException("bookId不能为空");
        }
        if(!StringUtils.hasText(chapterId)){
            throw new IllegalArgumentException("chapterId不能为空");
        }
        if(themeId == null){
            throw new IllegalArgumentException("themeId不能为空");
        }
        if(overrideDefault == null){
            throw new IllegalArgumentException("overrideDefault不能为空");
        }
        if(overrideCustom == null){
            throw new IllegalArgumentException("overrideCustom不能为空");
        }
        if(editorTemplate == null){
            throw new IllegalArgumentException("editorTemplate不能为空");
        }
    }
}
