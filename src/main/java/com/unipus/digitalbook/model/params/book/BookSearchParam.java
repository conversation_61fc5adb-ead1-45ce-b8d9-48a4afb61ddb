package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "教材搜索参数")
public class BookSearchParam implements Params {
    @Schema(description = "教材名称")
    private String bookName;

    @Schema(description = "系列ID")
    private Long seriesId;

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public Long getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Long seriesId) {
        this.seriesId = seriesId;
    }

    @Override
    public void valid() {
    }
}
