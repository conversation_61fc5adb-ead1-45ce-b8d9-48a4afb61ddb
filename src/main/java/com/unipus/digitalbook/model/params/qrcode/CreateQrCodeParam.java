package com.unipus.digitalbook.model.params.qrcode;

import com.unipus.digitalbook.model.enums.QrCodeSizeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "创建二维码参数")
public class CreateQrCodeParam implements Params {

    @Schema(description = "教材ID（必填）")
    private String bookId;

    @Schema(description = "教材名称（必填）")
    private String bookName;

    @Schema(description = "生成数量（必填）")
    private Integer count;

    @Schema(description = "二维码大小（必填）：SMALL-小尺寸(2.0cm)，LARGE-大尺寸(2.5cm)")
    private QrCodeSizeEnum size;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public QrCodeSizeEnum getSize() {
        return size;
    }

    public void setSize(QrCodeSizeEnum size) {
        this.size = size;
    }

    public void valid() {
        // 验证教材ID是否为空
        if (bookId == null || bookId.isEmpty()) {
            throw new IllegalArgumentException("bookId不能为空");
        }
        // 验证教材名称是否为空
        if (bookName == null || bookName.trim().isEmpty()) {
            throw new IllegalArgumentException("请选择教材名称");
        }
        if (count == null) {
            throw new IllegalArgumentException("请输入生成数量");
        }
        // 验证生成数量是否在1到100之间
        if (count <= 0 || count > 100) {
            throw new IllegalArgumentException("请输入大于0，100以内的整数");
        }
        // 验证二维码大小是否为空
        if (size == null) {
            throw new IllegalArgumentException("请选择二维码大小");
        }
    }

}
