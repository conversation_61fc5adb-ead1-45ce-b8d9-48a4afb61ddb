package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.enums.StatusEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "新增角色请求参数")
public class AddRoleParam implements Params {
    @Schema(description = "角色描述")
    private String desc;
    @Schema(description = "角色名称")
    private String name;
    @Schema(description = "角色状态")
    private Integer status = StatusEnum.ENABLE.getCode();

    public Role toRole(Long userId) {
        Role role = new Role();
        role.setDesc(desc);
        role.setName(name);
        role.setStatus(status);
        role.setCreateBy(userId);
        role.setUpdateBy(userId);
        return role;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public void valid() {
        if (StringUtils.isBlank(this.getName())) {
            throw new IllegalArgumentException("请输入角色名称");
        }
        if (this.getName().length() > 10) {
            throw new IllegalArgumentException("角色名字长度不能超过10个字符");
        }
        if (this.getDesc().length() > 50) {
            throw new IllegalArgumentException("角色说明长度不能超过50个字符");
        }
    }
}
