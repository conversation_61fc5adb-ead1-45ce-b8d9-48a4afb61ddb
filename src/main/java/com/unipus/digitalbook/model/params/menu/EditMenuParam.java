package com.unipus.digitalbook.model.params.menu;

import com.unipus.digitalbook.model.entity.menu.Menu;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Schema(description = "编辑菜单参数")
public class EditMenuParam implements Params {
    @Schema(description = "菜单id", example = "1")
    private Long id;
    @Schema(description = "父级菜单Id", example = "0")
    private Long parentId;
    @Schema(description = "菜单名字", example = "一级菜单")
    private String name;
    @Schema(description = "菜单路径", example = "index")
    private String path;

    public Menu toMenu(Long userId) {
        Menu menu = new Menu();
        menu.setId(this.getId());
        menu.setName(this.getName());
        menu.setPath(this.getPath());
        menu.setParentId(this.getParentId());
        menu.setCreateBy(userId);
        menu.setUpdateBy(userId);
        return menu;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public void valid() {
        if (this.getId() == null || this.getId() <= 0) {
            throw new IllegalArgumentException("菜单id不能为空");
        }
        if (Objects.equals(this.getParentId(), this.getId())) {
            throw new IllegalArgumentException("菜单id不能与父级菜单id相同");
        }
        if (StringUtils.isBlank(this.getName())) {
            throw new IllegalArgumentException("请输入菜单名称");
        }
        if (this.getName().length() > 10) {
            throw new IllegalArgumentException("请输入10位以内的字符");
        }
        if (!this.getName().matches("^[\\u4e00-\\u9fa5_a-zA-Z0-9]+$")) {
            throw new IllegalArgumentException("请输入中文，英文或数字");
        }
        if (StringUtils.isNotBlank(this.getPath()) && this.getPath().length() > 100) {
            throw new IllegalArgumentException("最大可输入100个字符");
        }
    }
}
