package com.unipus.digitalbook.model.params.assistant;

import com.unipus.digitalbook.model.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "教材模板引用关系查询参数")
public class AssistantTemplateReferenceParam implements Serializable {

    @Schema(description = "模板id")
    @NotBlank
    private String templateId;

    @Schema(description = "分页与排序")
    private PageParams pageParams = new PageParams();
}
