package com.unipus.digitalbook.model.params.cos;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 删除音频转码模版参数
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@Schema(description = "删除音频转码模版参数")
public class DeleteMediaTranscodeTemplateParam implements Params {

    @Schema(description = "模版ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateId;

    @Override
    public void valid() {
        if (!StringUtils.hasText(templateId)) {
            throw new IllegalArgumentException("模版ID不能为空");
        }
    }
}
