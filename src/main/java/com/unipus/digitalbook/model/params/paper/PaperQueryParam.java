package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

/**
 * 试卷信息查询参数
 */
@Schema(description = "试卷信息查询参数")
public class PaperQueryParam implements Params {

    @Schema(description = "教材ID (UUID)")
    private String bookId;
    @Schema(description = "测试名称")
    private String paperName;
    @Schema(description = "试卷类型 1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;

    @Override
    public void valid() {
        if(!StringUtils.hasText(bookId)){
            throw new IllegalArgumentException("教材ID不能为空");
        }
        if(this.paperType!=null && PaperTypeEnum.getByCode(this.paperType)== null){
            throw new IllegalArgumentException("试卷类型不正确");
        }
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }
}
