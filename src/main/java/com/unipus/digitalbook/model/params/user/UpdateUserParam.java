package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "更新用户入参")
public class UpdateUserParam implements Params {

    @Schema(description = "用户姓名", example = "张三")
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public UserInfo toEntities() {
        UserInfo userInfo = new UserInfo();
        userInfo.setName(name);
        return userInfo;
    }

    @Override
    public void valid() {

        // 昵称：必填，可编辑项，显示用户的昵称，验证规则：长度2-16位,
        if (name == null || name.length() < 2 || name.length() > 16) {
            throw new IllegalArgumentException("请输入2-16位字符");
        }
        // 首尾不能包含空格
        if (name.startsWith(" ") || name.endsWith(" ")) {
            throw new IllegalArgumentException("首位/末尾不可输入空格");
        }
        // 不可使用手机号
        if (name.matches("^1[3456789]\\d{9}$")) {
            throw new IllegalArgumentException("不可使用手机号");
        }
        // 支持汉字、字母、数字、"-"、"_"、空格（首尾除外）
        if (!name.matches("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s-_]+$")) {
            throw new IllegalArgumentException("请输入中文、字母、数字、-、_、空格（首尾除外）");
        }
    }
}
