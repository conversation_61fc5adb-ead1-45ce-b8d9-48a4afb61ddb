package com.unipus.digitalbook.model.params.book;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "教材发布设置参数")
public class BookSettingParam implements Serializable {

    @Schema(description = "教材id")
    @NotNull
    private String bookId;

    @Schema(description = "是否设置数字人")
    @NotNull
    private Boolean isAssistantSetting;

    @Schema(description = "数字人启用状态")
    private Boolean assistantOpened;

    @Schema(description = "数字人更新状态")
    private Boolean assistantUpdated;

    @Schema(description = "知识图谱更新状态")
    @NotNull
    private Boolean knowledgeUpdated;

    @Schema(description = "教材和知识图谱关联关系id")
    private Long id;

    @Schema(description = "知识图谱id")
    private String knowledgeId;

}
