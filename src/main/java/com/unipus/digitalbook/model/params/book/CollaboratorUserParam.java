package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 教材协作者查询参数
 */
public class CollaboratorUserParam implements Params {

    @Schema(description = "章节id")
    private String chapterId;

    @Schema(description = "关键字")
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    @Override
    public void valid() {
        if(chapterId == null) {
            throw new IllegalArgumentException("章节id不能为空");
        }
    }
}
