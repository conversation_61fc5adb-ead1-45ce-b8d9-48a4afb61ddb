package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "角色搜索请求参数")
public class RoleSearchParam implements Params {
    @Schema(description = "角色名称")
    private String name;
    @Schema(description = "是否启用1启用")
    private Integer status;
    @Schema(description = "组织ID", example = "1")
    private Long orgId;
    @Schema(description = "分页参数",example = "0")
    private PageParams pageParams;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    @Override
    public void valid() {
        if (this.pageParams == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
    }
}
