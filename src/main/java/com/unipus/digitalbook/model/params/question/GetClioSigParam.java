package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "生成用户作答clio引擎业务参数")
public class GetClioSigParam implements Params {

    @Schema(description = "题目业务ID")
    private String bizQuestionId;

    @Schema(description = "题型")
    private String questionType;

    @Schema(description = "soe题型")
    private String soeQuestionType;

    public String getBizQuestionId() {
        return bizQuestionId;
    }

    public void setBizQuestionId(String bizQuestionId) {
        this.bizQuestionId = bizQuestionId;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getSoeQuestionType() {
        return soeQuestionType;
    }

    public void setSoeQuestionType(String soeQuestionType) {
        this.soeQuestionType = soeQuestionType;
    }

    @Override
    public void valid() {
        if (!StringUtils.hasText(bizQuestionId)) {
            throw new IllegalArgumentException("题目业务ID不能为空");
        }
        if (!StringUtils.hasText(questionType)) {
            throw new IllegalArgumentException("题型不能为空");
        }
        if (QuestionTypeEnum.getEnumByName(questionType) == null) {
            throw new IllegalArgumentException("题型不正确");
        }
        if (!StringUtils.hasText(soeQuestionType)) {
            throw new IllegalArgumentException("soe题型不能为空");
        }
    }
}