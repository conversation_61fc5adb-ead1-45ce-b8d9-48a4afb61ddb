package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.params.Params;

import java.util.List;

public class SortChapterParam implements Params {

    private String bookId;

    private List<ChapterSort> chapterList;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public List<ChapterSort> getChapterList() {
        return chapterList;
    }

    public void setChapterList(List<ChapterSort> chapterList) {
        this.chapterList = chapterList;
    }


    public static class ChapterSort {
        private String chapterId;
        private int chapterNumber;

        public Chapter toEntity() {
            Chapter chapter = new Chapter();
            chapter.setId(chapterId);
            chapter.setChapterNumber(chapterNumber);
            return chapter;
        }

        public String getChapterId() {
            return chapterId;
        }

        public void setChapterId(String chapterId) {
            this.chapterId = chapterId;
        }

        public int getChapterNumber() {
            return chapterNumber;
        }

        public void setChapterNumber(int chapterNumber) {
            this.chapterNumber = chapterNumber;
        }
    }


    /**
     *
     */
    @Override
    public void valid() {
        if (bookId == null) {
            throw new IllegalArgumentException("bookId不能为空");
        }
        if (chapterList == null || chapterList.isEmpty()) {
            throw new IllegalArgumentException("chapterList不能为空");
        }
        for (ChapterSort chapterSort : chapterList) {
            if (chapterSort.getChapterId() == null) {
                throw new IllegalArgumentException("chapterId不能为空");
            }
            if (chapterSort.getChapterNumber() <= 0) {
                throw new IllegalArgumentException("chapterNumber不合法");
            }
        }

    }
}
