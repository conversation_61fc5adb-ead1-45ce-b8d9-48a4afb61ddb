package com.unipus.digitalbook.model.params.qrcode;

import com.unipus.digitalbook.model.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "二维码搜索参数")
public class SearchQrCodeParam implements Serializable {

    @Schema(description = "教材名称")
    private String bookName;

    @Schema(description = "分页与排序")
    private PageParams pageParams;

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }
}
