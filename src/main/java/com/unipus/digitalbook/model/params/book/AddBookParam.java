package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.enums.BusinessTypeEnum;
import com.unipus.digitalbook.model.enums.CourseNatureEnum;
import com.unipus.digitalbook.model.enums.LanguageEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

@Schema(description = "新增数字教材参数")
public class AddBookParam implements Params {
    @Schema(description = "教材中文名称")
    private String chineseName;

    /**
     * 教材英文名称
     */
    @Schema(description = "教材英文名称")
    private String englishName;

    /**
     * 语种
     */
    @Schema(description = "语种")
    private String language;

    /**
     * 教材业务类型
     */
    @Schema(description = "教材业务类型")
    private String businessType;

    /**
     * 教材系列Id
     */
    @Schema(description = "教材系列Id")
    private Long seriesId;

    /**
     * 对应课程
     */
    @Schema(description = "对应课程")
    private String course;

    /**
     * 课程性质
     */
    @Schema(description = "课程性质")
    private String courseNature;

    /**
     * 适用专业
     */
    @Schema(description = "适用专业")
    private String applicableMajor;

    /**
     * 适用年级
     */
    @Schema(description = "适用年级")
    private String applicableGrade;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱")
    private String contactEmail;

    /**
     * 是否纯数字教材  0-否 1-是
     */
    @Schema(description = "是否纯数字教材  0-否 1-是")
    private Boolean digitalFlag;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        // 教材信息区域验证
        if (this.getChineseName() == null || this.getChineseName().isEmpty()) {
            result.addError("教材中文名称", "请输入教材中文名称");
        } else if (this.getChineseName().length() > 100) {
            result.addError("教材中文名称", "最长可输入100个字符");
        }

        if (this.getEnglishName() != null && this.getEnglishName().length() > 100) {
            result.addError("教材英文名称", "最长可输入100个字符");
        }

        if (this.getLanguage() == null) {
            result.addError("语种", "请选择语种");
        }
        if (this.getLanguage() != null && LanguageEnum.getByCode(this.getLanguage()) == null) {
            result.addError("语种", "不存在该语种");
        }

        if (this.getBusinessType() != null
                && BusinessTypeEnum.getByCode(this.getBusinessType()) == null) {
            result.addError("教材业务类型", "不存在该业务类型");
        }

        // 教材用途区域验证
        if (this.getCourse() == null || this.getCourse().isEmpty()) {
            result.addError("对应课程", "请输入对应课程");
        } else if (this.getCourse().length() > 50) {
            result.addError("对应课程", "最长可输入50个字符");
        }

        if (this.getCourseNature() == null || this.getCourseNature().isEmpty()) {
            result.addError("课程性质", "请选择课程性质");
        }
        if (this.getCourseNature() != null && CourseNatureEnum.getByCode(this.getCourseNature()) == null) {
            result.addError("课程性质", "课程性质只能是必修，限选");
        }

        if (this.getApplicableMajor() == null || this.getApplicableMajor().isEmpty()) {
            result.addError("适用专业", "请输入适用专业");
        } else if (this.getApplicableMajor().length() > 50) {
            result.addError("适用专业", "最长可输入50个字符");
        }

        if (this.getApplicableGrade() == null || this.getApplicableGrade().isEmpty()) {
            result.addError("适用年级", "请输入适用年级");
        } else if (this.getApplicableGrade().length() > 50) {
            result.addError("适用年级", "最长可输入50个字符");
        }

        // 教师联系方式区域验证
        String phonePattern = "^[\\d-]{1,20}$";
        if (StringUtils.hasText(this.getContactPhone()) && !Pattern.matches(phonePattern, this.getContactPhone())) {
            result.addError("联系电话", "请输入有效的联系电话");
        }

        String emailPattern = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        if (StringUtils.hasText(this.getContactEmail()) && !Pattern.matches(emailPattern, this.getContactEmail())) {
            result.addError("联系邮箱", "请输入有效的邮箱");
        } else if (StringUtils.hasText(this.getContactEmail()) && this.getContactEmail().length() > 50) {
            result.addError("联系邮箱", "最长可输入50个字符");
        }

        if (this.getDigitalFlag() == null) {
            result.addError("是否纯数字教材", "请选择是否纯数字教材");
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }

    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }


    public String getCourse() {
        return course;
    }

    public void setCourse(String course) {
        this.course = course;
    }

    public String getCourseNature() {
        return courseNature;
    }

    public void setCourseNature(String courseNature) {
        this.courseNature = courseNature;
    }

    public String getApplicableMajor() {
        return applicableMajor;
    }

    public void setApplicableMajor(String applicableMajor) {
        this.applicableMajor = applicableMajor;
    }

    public String getApplicableGrade() {
        return applicableGrade;
    }

    public void setApplicableGrade(String applicableGrade) {
        this.applicableGrade = applicableGrade;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public Book toEntity(Long orgId) {
        Book book = new Book();
        book.setChineseName(getChineseName());
        book.setEnglishName(getEnglishName());
        book.setLanguage(getLanguage());
        book.setBusinessType(getBusinessType());
        book.setSeriesId(getSeriesId());
        book.setCourse(getCourse());
        book.setCourseNature(getCourseNature());
        book.setApplicableMajor(getApplicableMajor());
        book.setApplicableGrade(getApplicableGrade());
        book.setContactPhone(getContactPhone());
        book.setContactEmail(getContactEmail());
        book.setOrgId(orgId);
        book.setDigitalFlag(getDigitalFlag());
        return book;
    }


    public Long getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Long seriesId) {
        this.seriesId = seriesId;
    }

    public Boolean getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(Boolean digitalFlag) {
        this.digitalFlag = digitalFlag;
    }
}
