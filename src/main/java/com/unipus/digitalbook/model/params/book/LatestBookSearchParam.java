package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "教材搜索参数")
public class LatestBookSearchParam implements Serializable {
    @Schema(description = "教材名称")
    private String bookName;

    @Schema(description = "系列ID")
    private Long seriesId;

    @Schema(description = "分页与排序")
    private PageParams pageParams = new PageParams();

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public Long getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Long seriesId) {
        this.seriesId = seriesId;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }
}
