package com.unipus.digitalbook.model.params.knowledge;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeGraphInfo;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeAddRequest;
import org.apache.poi.util.StringUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 */
public class KnowledgeAddParam implements Params {

    @JsonAlias(value = "courseIdStr")
    private String booId;
    private String name;
    private String description;
    private String background;
    private String userId;
    private String userName;
    private List<KnowledgeGraphInfo> graphList;


    // Getters and Setters


    public String getBooId() {
        return booId;
    }

    public void setBooId(String booId) {
        this.booId = booId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public List<KnowledgeGraphInfo> getGraphList() {
        return graphList;
    }

    public void setGraphList(List<KnowledgeGraphInfo> graphList) {
        this.graphList = graphList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();
        // 教材资源信息验证
        if (StringUtil.isBlank(this.getBooId())) {
            result.addError("教材Id为空", "请输入教材Id");
        }

        if (StringUtil.isBlank(this.getName())) {
            result.addError("图谱名称为空", "请输入图谱名称");
        }

        if (CollectionUtils.isEmpty(this.getGraphList())) {
            result.addError("图谱子图为空", "请输入图谱子图");
        }

        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }

    public KnowledgeAddRequest toKnowledgeAddRequest(){
        KnowledgeAddRequest addRequest = new KnowledgeAddRequest();
        addRequest.setBookId(this.getBooId());
        addRequest.setName(this.getName());
        addRequest.setDescription(this.getDescription());
        addRequest.setBackground(this.getBackground());
        addRequest.setGraphList(this.getGraphList());
        addRequest.setUserId(this.getUserId());
        addRequest.setUserName(this.getUserName());
        return addRequest;

    }
}
