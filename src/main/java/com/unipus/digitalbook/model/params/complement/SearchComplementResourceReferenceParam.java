package com.unipus.digitalbook.model.params.complement;

import com.unipus.digitalbook.model.entity.complement.ComplementResourceReference;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "配套资源引用查询参数")
public class SearchComplementResourceReferenceParam implements Params {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "配套资源ID")
    private String complementResourceId;

    @Schema(description = "媒体位置(新增时不可为空，编辑时可为空)")
    private String position;

    @Schema(description = "章节id(新增时不可为空，编辑时可为空)")
    private String chapterId;

    // Getter and Setter methods

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComplementResourceId() {
        return complementResourceId;
    }

    public void setComplementResourceId(String complementResourceId) {
        this.complementResourceId = complementResourceId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    @Override
    public void valid() {
        if(id != null) {
            return;
        }
        if(StringUtils.hasText(this.complementResourceId)) {
            return;
        }
        if (StringUtils.hasText(this.position)) {
            return;
        }
        if(StringUtils.hasText(this.chapterId)) {
            return;
        }
        throw new IllegalArgumentException("参数不能都为空");
    }

    public ComplementResourceReference toEntity() {
        ComplementResourceReference entity = new ComplementResourceReference();
        entity.setId(this.id);
        entity.setComplementResourceId(this.complementResourceId);
        entity.setPosition(this.position);
        entity.setChapterId(this.chapterId);
        entity.setEnable(true);
        return entity;
    }

}
