package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.FillBlankQuestion;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 填空题参数
 */
public class FillBlankQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(getChildren())) {
            if (CollectionUtils.isEmpty(getAnswers())) {
                throw new IllegalArgumentException("答案不能为空");
            }
        }
    }
    @Override
    protected Question toQuestion(QuestionText questionText) {
        FillBlankQuestion fillBlankQuestion = new FillBlankQuestion();
        fillBlankQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return fillBlankQuestion;
    }
}
