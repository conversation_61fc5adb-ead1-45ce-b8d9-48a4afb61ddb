package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "教材发布日志查询")
public class BookSettingLogParam implements Serializable {

    @Schema(description = "教材id")
    @NotNull
    private String bookId;

    @Schema(description = "操作人")
    private String name;

    @Schema(description = "开始时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Long beginTime;

    @Schema(description = "结束时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Long endTime;

    @Schema(description = "分页与排序")
    @NotNull
    private PageParams pageParams;

}
