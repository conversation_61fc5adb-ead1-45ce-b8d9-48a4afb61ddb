package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.entity.role.RolePermission;
import com.unipus.digitalbook.model.entity.role.RolePermissionItem;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "角色新增接口权限请求参数")
public class AddInterfaceParam implements Params {
    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "接口路径")
    private List<String> interfacePathList;

    public RolePermission toEntity() {
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleId(roleId);
        rolePermission.setPermissionItems(interfacePathList.stream()
                .map(r -> new RolePermissionItem(r, true)).toList());
        return rolePermission;
    }
    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public List<String> getInterfacePathList() {
        return interfacePathList;
    }

    public void setInterfacePathList(List<String> interfacePathList) {
        this.interfacePathList = interfacePathList;
    }

    @Override
    public void valid() {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        if (interfacePathList == null || interfacePathList.isEmpty()) {
            throw new IllegalArgumentException("请选择至少一条数据");
        }
    }
}
