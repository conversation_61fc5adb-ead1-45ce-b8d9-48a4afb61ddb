package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 教材协作者参数
 */
@Schema(description = "教材协作者参数")
public class BookCollaboratorParam implements Params {

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "章节ID")
    private String chapterId;

    @Schema(description = "用户ID列表")
    private List<Long> userIds;

    public String getBookId() {
        return bookId;
    }
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }
    public String getChapterId() {
        return chapterId;
    }
    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }
    public List<Long> getUserIds() {
        return userIds;
    }
    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    @Override
    public void valid() {
        if (bookId == null) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        if (chapterId == null) {
            throw new IllegalArgumentException("章节ID不能为空");
        }
    }
}
