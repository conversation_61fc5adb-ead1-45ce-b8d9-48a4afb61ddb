package com.unipus.digitalbook.model.params.complement;

import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.enums.ComplementSuffixEnum;
import com.unipus.digitalbook.model.enums.ComplementTypeEnum;
import com.unipus.digitalbook.model.enums.VisibleStatusEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "新增资源参数")
public class AddResourceParam implements Params {
    @Schema(description = "教材ID", example = "教材ID")
    private String bookId;
    @Schema(description = "资源名称", example = "资源名称")
    private String name;

    @Schema(description = "资源后缀", example = "jpg")
    private String suffix;
    @Schema(description = "资源地址", example = "xxx.jpg")
    private String resourceUrl;
    @Schema(description = "资源大小, 整数，单位b", example = "1000")
    private Long size;
    @Schema(description = "时长，单位毫秒", example = "1000")
    private Long duration;
    @Schema(description = "封面图", example = "xxx.jpg")
    private String coverUrl;
    @Schema(description = "资源类型", example = "1")
    private Integer resourceType;

    public ComplementResource toEntity(Long userId) {
        ComplementResource resource = new ComplementResource();
        resource.setName(this.name);
        resource.setBookId(this.bookId);
        resource.setSuffix(this.suffix);
        resource.setDuration(duration);
        ComplementSuffixEnum complementSuffix = ComplementSuffixEnum.fromSuffix(this.suffix);
        if (complementSuffix == null) {
            throw new IllegalArgumentException("所选格式不支持");
        }
        resource.setResourceType(complementSuffix.getType().getCode());
        resource.setResourceUrl(this.resourceUrl);
        resource.setSize(this.size);
        resource.setCoverUrl(this.coverUrl);
        resource.setCreateBy(userId);
        resource.setUpdateBy(userId);
        resource.setVisibleStatus(VisibleStatusEnum.ALL.getCode());
        return resource;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    @Override
    public void valid() {
        if (bookId == null || bookId.isEmpty()) {
            throw new IllegalArgumentException("bookId不能为空");
        }
        if (name == null || name.isEmpty()) {
            throw new IllegalArgumentException("资源名称不能为空");
        }
        if (suffix == null || suffix.isEmpty()) {
            throw new IllegalArgumentException("资源后缀不能为空");
        }
        if (resourceUrl == null || resourceUrl.isEmpty()) {
            throw new IllegalArgumentException("资源地址不能为空");
        }
        if (name.length() > 100) {
            throw new IllegalArgumentException("资源名称最长可输入100个字符");
        }
        ComplementSuffixEnum complementSuffix = ComplementSuffixEnum.fromSuffix(this.suffix);
        if (complementSuffix == null) {
            ComplementTypeEnum complementType = ComplementTypeEnum.getByCode(resourceType);
            throw new IllegalArgumentException(String.format("所选%s格式不支持", complementType == null ? "" : complementType.getDesc()));
        }
        if (size == null) {
            throw new IllegalArgumentException("资源大小不能为空");
        }
        if (size > complementSuffix.getType().getMaxSize()) {
            throw new IllegalArgumentException(String.format("请选择%s及以下的%s", complementSuffix.getType().getMaxSizeDesc(), complementSuffix.getType().getDesc()));
        }
    }
}
