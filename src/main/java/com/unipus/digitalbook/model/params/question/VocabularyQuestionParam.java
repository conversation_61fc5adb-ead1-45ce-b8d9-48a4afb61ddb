package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.VocabularyQuestion;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 单词本参数
 */
public class VocabularyQuestionParam extends QuestionBaseParam {


    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(getChildren())) {
            if (CollectionUtils.isEmpty(getAnswers())) {
                throw new IllegalArgumentException("单词不能为空");
            }
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        VocabularyQuestion question = new VocabularyQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setMedia(getMedia());
        currentQuestionText.setPhoneticSymbol(getPhoneticSymbol());
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        question.setQuestionText(currentQuestionText);
        return question;
    }
}
