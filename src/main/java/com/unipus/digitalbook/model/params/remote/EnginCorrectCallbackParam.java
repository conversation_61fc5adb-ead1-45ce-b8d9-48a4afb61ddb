package com.unipus.digitalbook.model.params.remote;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "写作引擎回调参数")
public class EnginCorrectCallbackParam {
    @Schema(description = "写作评测Id")
    private String icorrectId;

    @Schema(description = "翻译评测Id")
    private String aiTranslateId;

    public String getIcorrectId() {
        return icorrectId;
    }

    public void setIcorrectId(String icorrectId) {
        this.icorrectId = icorrectId;
    }

    public String getAiTranslateId() {
        return aiTranslateId;
    }

    public void setAiTranslateId(String aiTranslateId) {
        this.aiTranslateId = aiTranslateId;
    }

    @Override
    public String toString() {
        return "EnginCorrectCallbackParam{" +
                "icorrectId='" + icorrectId + '\'' +
                ", aiTranslateId='" + aiTranslateId + '\'' +
                '}';
    }
}
