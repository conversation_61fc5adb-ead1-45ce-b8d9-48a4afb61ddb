package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.entity.role.RoleUser;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "添加用户角色入参")
public class AssignUserRoleParam implements Params {
    @Schema(description = "用户id")
    private Long userId;
    @Schema(description = "组织id")
    private Long orgId;
    @Schema(description = "角色ids")
    private List<Long> roleIds;

    public RoleUser toEntities() {
        RoleUser roleUser = new RoleUser();
        roleUser.setRoleIds(roleIds);
        roleUser.setUserId(userId);
        roleUser.setOrgId(orgId);
        return roleUser;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public List<Long> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    @Override
    public void valid() {
        if (userId == null || orgId == null) {
            throw new IllegalArgumentException("参数错误");
        }
        if (roleIds == null || roleIds.isEmpty()) {
            throw new IllegalArgumentException("请选择角色");
        }
    }
}
