package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.params.question.BigQuestionGroupParam;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Schema(description = "教材自建内容信息参数")
public class CustomContentParam implements Params {

    @Schema(description = "内容业务ID")
    private String bizId;

    @Schema(description = "内容类型：1-自定义章节，2-自定义段落")
    private Integer type;

    @Schema(description = "内容名称")
    private String name;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "学生内容")
    private String studentContent;

    @Schema(description = "头图地址")
    private String headerImg;

    @Schema(description = "目录结构")
    private String catalog;

    @Schema(description = "资源信息")
    private String resource;

    @Schema(description = "整体结构节点列表")
    private List<CustomContentNodeParam> totalStructNodeList;

    @Schema(description = "内容的题")
    private List<BigQuestionGroupParam> questionList;

    @Schema(description = "内容数据包")
    private String contentPackage;

    public CustomContent toEntity(Long tenantId, Long userId) {
        CustomContent customContent = new CustomContent();
        customContent.setBizId(this.bizId);// 默认为编写中
        customContent.setType(this.type);
        customContent.setName(this.name);
        customContent.setContent(this.content);
        customContent.setStudentContent(this.studentContent);
        customContent.setHeaderImg(this.headerImg);
        customContent.setResource(this.resource);
        customContent.setTenantId(tenantId);
        customContent.setCreateBy(userId);
        customContent.setUpdateBy(userId);
        // 转换目录节点列表 - 区分null和空字符串，null时不设置，空字符串或有内容时进行解析
        if (this.catalog != null) {
            customContent.fillCatalogNodeList(this.catalog);
        }
        // 转换整体结构节点列表 - 区分null和空集合，null时不设置，空集合时设置为空列表
        if (totalStructNodeList != null) {
            customContent.setTotalStructNodeList(totalStructNodeList.stream()
                    .map(CustomContentNodeParam::toEntity)
                    .toList());
        }
        // 转换题目列表
        if (questionList != null) {
            customContent.setQuestionList(questionList.stream()
                    .map(q -> q.toEntity(userId, null))
                    .toList());
        }
        return customContent;
    }

    @Override
    public void valid() {
        if (StringUtils.isBlank(this.bizId)) {
            throw new IllegalArgumentException("bizId不能为空");
        }
        if (this.type == null) {
            throw new IllegalArgumentException("内容类型不能为空");
        }
        if (PaperTypeEnum.getByCode(this.type) == null) {
            throw new IllegalArgumentException("无效的内容类型");
        }
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public List<CustomContentNodeParam> getTotalStructNodeList() {
        return totalStructNodeList;
    }

    public void setTotalStructNodeList(List<CustomContentNodeParam> totalStructNodeList) {
        this.totalStructNodeList = totalStructNodeList;
    }

    public List<BigQuestionGroupParam> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<BigQuestionGroupParam> questionList) {
        this.questionList = questionList;
    }

    public String getContentPackage() {
        return contentPackage;
    }

    public void setContentPackage(String contentPackage) {
        this.contentPackage = contentPackage;
    }
}
