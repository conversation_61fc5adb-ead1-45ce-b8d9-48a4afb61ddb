package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.ResultMessage;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 题组参数列表实体类
 */
@Schema(description = "题组参数列表实体类")
public class QuestionGroupListParam implements Params {

    @Schema(description = "题组参数参数列表")
    private List<BigQuestionGroupParam> questionGroupParams;

    public List<BigQuestionGroupParam> getQuestionGroupParams() {
        return questionGroupParams;
    }

    public void setQuestionGroupParams(List<BigQuestionGroupParam> questionGroupParams) {
        this.questionGroupParams = questionGroupParams;
    }

    public List<BigQuestionGroup> toEntity(Long userId, Long parentId) {
        return questionGroupParams.stream().map(bigQuestionGroupParam -> {
            BigQuestionGroup entity = bigQuestionGroupParam.toEntity(userId, null);
            entity.setParentId(parentId);
            return entity;
        }).toList();
    }

    @Override
    public void valid() {
        if(CollectionUtils.isEmpty(this.questionGroupParams)){
            throw new IllegalArgumentException(ResultMessage.BIZ_QUES_PARAM_IS_NULL.getMessage());
        }

        this.questionGroupParams.forEach(Params::valid);
    }
}
