package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.ShortAnswerQuestion;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

public class ShortAnswerQuestionParam extends QuestionBaseParam{

    @Override
    public void valid() {
        if (!CollectionUtils.isEmpty(getChildren())) {
            if (!StringUtils.hasText(getQuesText())) {
                throw new IllegalArgumentException("题干不能为空");
            }
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        ShortAnswerQuestion shortAnswerQuestion = new ShortAnswerQuestion();
        QuestionText currentQuestionText = new QuestionText();
        currentQuestionText.setAnswerWordLimit(getAnswerWordLimit());
        shortAnswerQuestion.setQuestionText(currentQuestionText);
        return shortAnswerQuestion;
    }
}
