package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "题型解析参数")
public class QuestionParseParam implements Params {

    @Schema(description = "题型类型", example = "single-choice")
    private String questionType;

    @Schema(description = "图片路径", example = "https://example.com/image.jpg")
    private String imageUrl;

    @Schema(description = "文本内容", example = "这是一个单选题")
    private String text;

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        if (StringUtils.isBlank(questionType)) {
            result.addError("questionType", "题型类型不能为空");
        }
        if (StringUtils.isBlank(imageUrl) && StringUtils.isBlank(text)) {
            result.addError("imageUrl与text", "图片路径与文本内容不能全为空");
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }
    }
}
