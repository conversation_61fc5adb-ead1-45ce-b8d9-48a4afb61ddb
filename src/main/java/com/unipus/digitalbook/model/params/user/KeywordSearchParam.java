package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

/**
 * 用户检索参数
 */
@Schema(description = "用户检索参数")
public class KeywordSearchParam implements Params {

    @Schema(description = "组织ID(非必须)")
    private Long orgId;

    @Schema(description = "关键字(必须):模糊匹配用户昵称/精确匹配手机号码", example = "nickname/cellphone")
    private String keyword;

    @Schema(description = "分页参数(非必须)")
    private PageParams pageParams;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    @Override
    public void valid() {
        if(!StringUtils.hasText(this.keyword)){
            throw new IllegalArgumentException("关键字不能为空");
        }
    }
}

