package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "编辑用户入参")
public class EditUser4AdminParam implements Params {

    @Schema(description = "用户id", example = "100")
    private Long id;
    @Schema(description = "用户姓名", example = "张三")
    private String name;

    @Schema(description = "手机号", example = "13800138000")
    private String cellPhone;

    public String getName() {
        return name;
    }
    public String getCellPhone() {
        return cellPhone;
    }


    public UserInfo toEntity() {
        // 创建一个新的UserInfo对象
        UserInfo userInfo = new UserInfo();
        // 设置用户的id
        userInfo.setId(this.getId());
        // 设置用户的姓名
        userInfo.setName(this.getName());
        // 设置用户的手机号
        userInfo.setCellPhone(this.getCellPhone());
        // 返回填充了部分信息的UserInfo对象
        return userInfo;
    }

    /**
     *
     */
    @Override
    public void valid() {

        // 用户昵称：
        String name = this.getName();
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("请输入用户昵称");
        }
        if (name.length() < 2 || name.length() > 16) {
            throw new IllegalArgumentException("请输入2-16位字符");
        }
        if (name.startsWith(" ") || name.endsWith(" ")) {
            throw new IllegalArgumentException("首位不可输入空格");
        }
        if (!name.matches("[\\u4e00-\\u9fa5a-zA-Z0-9-_ ]+")) {
            throw new IllegalArgumentException("请输入中文、字母、数字、\"-\"、\"_\"、空格（首尾除外）");
        }
        if (name.matches("^1[0-9]{10}$")) {
            throw new IllegalArgumentException("不可使用手机号");
        }

        // 注册手机号：
        String cellPhone = this.getCellPhone();
        if (cellPhone == null || cellPhone.trim().isEmpty()) {
            throw new IllegalArgumentException("请输入注册手机号");
        }
        if (!cellPhone.matches("^1[0-9]{10}$")) {
            throw new IllegalArgumentException("请输入以1开头的11位的手机号");
        }

    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
