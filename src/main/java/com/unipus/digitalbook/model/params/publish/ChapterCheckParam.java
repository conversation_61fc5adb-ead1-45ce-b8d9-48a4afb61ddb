package com.unipus.digitalbook.model.params.publish;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "章节版本查询参数")
public class ChapterCheckParam implements Params {

    @Schema(description = "章节ID")
    private String chapterId;

    @Schema(description = "分页与排序")
    private PageParams pageParams;

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    @Override
    public void valid() {
        if (chapterId == null) {
            throw new IllegalArgumentException("chapterId不能为空");
        }
    }
}
