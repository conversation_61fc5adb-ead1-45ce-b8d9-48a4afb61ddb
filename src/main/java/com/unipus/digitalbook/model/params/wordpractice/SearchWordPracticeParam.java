package com.unipus.digitalbook.model.params.wordpractice;

import com.unipus.digitalbook.model.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "词汇学练请求参数")
public class SearchWordPracticeParam implements Serializable {

    @Schema(description = "章节ID")
    private String chapterId;

    @Schema(description = "分页与排序")
    private PageParams pageParams;
}

