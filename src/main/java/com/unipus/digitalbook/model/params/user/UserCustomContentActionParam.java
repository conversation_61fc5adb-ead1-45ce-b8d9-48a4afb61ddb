package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

public class UserCustomContentActionParam {
    @Schema(description = "自定义内容id")
    private String bizId;
    @Schema(description = "节点ID")
    private String nodeId;
    @Schema(description = "节点类型")
    private String nodeType;
    @Schema(description = "行为类型：finishNode")
    private String actionType;
    @Schema(description = "行为参数: 行为开始时间")
    private Long a;
    @Schema(description = "行为参数: 行为结束时间")
    private Long b;
    @Schema(description = "教材id")
    private String bookId;
    @Schema(description = "教材版本")
    private String bookVersionNumber;

    public UserAction toEntity(Long tenantId, String openId, String dataPackage, String ip) {
        UserAction userAction = new UserAction();
        userAction.setTenantId(tenantId);
        userAction.setOpenId(openId);
        userAction.setNodeId(nodeId);
        userAction.setContentId(bizId);
        userAction.setA(a);
        userAction.setB(b);
        userAction.setDataPackage(dataPackage);
        userAction.setIp(ip);
        userAction.setContentType(ContentTypeEnum.CUSTOM_CONTENT);
        userAction.setBookId(bookId);
        userAction.setBookVersionNumber(bookVersionNumber);
        return userAction;
    }
    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public Long getA() {
        return a;
    }

    public void setA(Long a) {
        this.a = a;
    }

    public Long getB() {
        return b;
    }

    public void setB(Long b) {
        this.b = b;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }
}
