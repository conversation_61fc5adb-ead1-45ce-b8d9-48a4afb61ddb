package com.unipus.digitalbook.model.params.menu;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Schema(description = "改变位置", example = "1")
public class ChangePositionParam implements Params {
    @Schema(description = "待排序的菜单列表")
    private List<Long> menuIds;

    public List<Long> getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(List<Long> menuIds) {
        this.menuIds = menuIds;
    }

    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(menuIds)) {
            throw new IllegalArgumentException("菜单列表不能为空");
        }
    }
}
