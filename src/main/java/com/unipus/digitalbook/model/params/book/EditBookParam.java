package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.enums.BusinessTypeEnum;
import com.unipus.digitalbook.model.enums.CourseNatureEnum;
import com.unipus.digitalbook.model.enums.LanguageEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;


@Schema(description = "编辑教材入参")
public class EditBookParam implements Params {
    @Schema(description = "教材id")
    private String bookId;
    @Schema(description = "中文名称")
    private String chineseName;
    @Schema(description = "英文名称")
    private String englishName;
    @Schema(description = "语言")
    private String language;
    @Schema(description = "业务类型")
    private String businessType;
    @Schema(description = "课程")
    private String course;
    @Schema(description = "教材系列Id")
    private Long seriesId;
    @Schema(description = "适用专业")
    private String applicableMajor;

    @Schema(description = "适用年级")
    private String applicableGrade;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "课程性质")
    private String courseNature;

    @Schema(description = "是否纯数字教材  0-否 1-是")
    private Boolean digitalFlag;

    @Override
    public void valid() {
        if (this.getChineseName() == null || this.getChineseName().isEmpty()) {
            throw new IllegalArgumentException("请输入教材中文名称");
        }
        if (this.getChineseName().length() > 100) {
            throw new IllegalArgumentException("最长可输入100个字符");
        }
        if (StringUtils.isNotBlank(this.getEnglishName()) && this.getEnglishName().length() > 100) {
            throw new IllegalArgumentException("最长可输入100个字符");
        }
        if (this.getLanguage() == null) {
            throw new IllegalArgumentException("请选择语种");
        }
        if (this.getLanguage() != null && LanguageEnum.getByCode(this.getLanguage()) == null) {
            throw new IllegalArgumentException("不存在该语种");
        }
        if (this.getBusinessType() != null && BusinessTypeEnum.getByCode(this.getBusinessType()) == null) {
            throw new IllegalArgumentException("不存在该业务类型");
        }
        if (this.getCourse() == null || this.getCourse().isEmpty()) {
            throw new IllegalArgumentException("请输入对应课程");
        }
        if (this.getCourse().length() > 50) {
            throw new IllegalArgumentException("最长可输入50个字符");
        }
        if (this.getCourseNature() == null || this.getCourseNature().isEmpty()) {
            throw new IllegalArgumentException("请选择课程性质");
        }
        if (this.getCourseNature() != null && CourseNatureEnum.getByCode(this.getCourseNature()) == null) {
            throw new IllegalArgumentException("课程性质只能是必修，限选");
        }

        if (this.getApplicableMajor() == null || this.getApplicableMajor().isEmpty()) {
            throw new IllegalArgumentException("请输入适用专业");
        }
        if (this.getApplicableMajor().length() > 50) {
            throw new IllegalArgumentException("最长可输入50个字符");
        }

        if (this.getApplicableGrade() == null || this.getApplicableGrade().isEmpty()) {
            throw new IllegalArgumentException("请输入适用年级");
        }

        if (this.getApplicableGrade().length() > 50) {
            throw new IllegalArgumentException("最长可输入50个字符");
        }
        // 教师联系方式区域验证
        String phonePattern = "^[\\d-]{1,20}$";
        if (org.springframework.util.StringUtils.hasText(this.getContactPhone()) && !Pattern.matches(phonePattern, this.getContactPhone())) {
            throw new IllegalArgumentException("请输入有效的联系电话");
        }
        String emailPattern = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        if (org.springframework.util.StringUtils.hasText(this.getContactEmail()) && !Pattern.matches(emailPattern, this.getContactEmail())) {
            throw new IllegalArgumentException("请输入有效的邮箱");
        }
        if (org.springframework.util.StringUtils.hasText(this.getContactEmail()) && this.getContactEmail().length() > 50) {
            throw new IllegalArgumentException("最长可输入50个字符");
        }
        if (this.getDigitalFlag() == null) {
            throw new IllegalArgumentException("请选择是否纯数字教材");
        }
    }

    public Book toEntity(Long userId) {
        Book book = new Book();
        if (StringUtils.isBlank(bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        book.setId(bookId);
        book.setChineseName(chineseName);
        book.setEnglishName(englishName);
        book.setLanguage(language);
        book.setBusinessType(businessType);
        book.setCourse(course);
        book.setSeriesId(seriesId);
        book.setApplicableMajor(applicableMajor);
        book.setApplicableGrade(applicableGrade);
        book.setContactPhone(contactPhone);
        book.setContactEmail(contactEmail);
        book.setCourseNature(courseNature);
        book.setUpdateBy(userId);
        book.setDigitalFlag(getDigitalFlag());
        return book;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getCourse() {
        return course;
    }

    public void setCourse(String course) {
        this.course = course;
    }

    public Long getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Long seriesId) {
        this.seriesId = seriesId;
    }

    public String getApplicableMajor() {
        return applicableMajor;
    }

    public void setApplicableMajor(String applicableMajor) {
        this.applicableMajor = applicableMajor;
    }

    public String getApplicableGrade() {
        return applicableGrade;
    }

    public void setApplicableGrade(String applicableGrade) {
        this.applicableGrade = applicableGrade;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getCourseNature() {
        return courseNature;
    }

    public void setCourseNature(String courseNature) {
        this.courseNature = courseNature;
    }

    public Boolean getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(Boolean digitalFlag) {
        this.digitalFlag = digitalFlag;
    }
}
