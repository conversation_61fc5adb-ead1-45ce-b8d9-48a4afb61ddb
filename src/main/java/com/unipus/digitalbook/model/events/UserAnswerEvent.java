package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.SubmitAnswerContext;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class UserAnswerEvent extends ApplicationEvent {

    private List<UserAnswer> userAnswers;

    private BigQuestionGroup question;

    private EventTypeEnum eventType;

    private SubmitAnswerContext context;

    public UserAnswerEvent(Object source, EventTypeEnum eventType, BigQuestionGroup question, List<UserAnswer> userAnswers, SubmitAnswerContext context) {
        super(source);
        this.userAnswers = userAnswers;
        this.question = question;
        this.eventType = eventType;
        this.context = context;
    }

    public List<UserAnswer> getUserAnswers() {
        return userAnswers;
    }

    public void setUserAnswers(List<UserAnswer> userAnswers) {
        this.userAnswers = userAnswers;
    }

    public BigQuestionGroup getQuestion() {
        return question;
    }

    public void setQuestion(BigQuestionGroup question) {
        this.question = question;
    }

    public EventTypeEnum getEventType() {
        return eventType;
    }

    public void setEventType(EventTypeEnum eventType) {
        this.eventType = eventType;
    }

    public SubmitAnswerContext getContext() {
        return context;
    }

    public void setContext(SubmitAnswerContext context) {
        this.context = context;
    }
}
