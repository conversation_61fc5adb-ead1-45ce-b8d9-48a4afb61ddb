package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.enums.EventTypeEnum;
import org.springframework.context.ApplicationEvent;

public class BookEvent extends ApplicationEvent {
    private String bookId;

    private EventTypeEnum eventType;

    private Long opsUserId;

    public BookEvent(Object source, String bookId, EventTypeEnum eventType, Long opsUserId) {
        super(source);
        this.bookId = bookId;
        this.eventType = eventType;
        this.opsUserId = opsUserId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public EventTypeEnum getEventType() {
        return eventType;
    }

    public void setEventType(EventTypeEnum eventType) {
        this.eventType = eventType;
    }

    public Long getOpsUserId() {
        return opsUserId;
    }

    public void setOpsUserId(Long opsUserId) {
        this.opsUserId = opsUserId;
    }
}
