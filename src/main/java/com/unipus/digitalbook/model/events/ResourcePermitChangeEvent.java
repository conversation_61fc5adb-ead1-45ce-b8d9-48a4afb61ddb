package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 资源权限变更事件实体
 */
public class ResourcePermitChangeEvent extends ApplicationEvent {

    // 变更参数
    private List<ResourcePermission> params;

    // 操作人
    private Long opUserId;

    public ResourcePermitChangeEvent(Object source, List<ResourcePermission> params, Long opUserId) {
        super(source);
        this.params = params;
        this.opUserId = opUserId;
    }

    public List<ResourcePermission> getParams() {
        return params;
    }

    public void setParams(List<ResourcePermission> params) {
        this.params = params;
    }

    public Long getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(Long opUserId) {
        this.opUserId = opUserId;
    }
}
