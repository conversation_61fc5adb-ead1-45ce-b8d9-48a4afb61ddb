package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import org.springframework.context.ApplicationEvent;

public class QuestionEvent extends ApplicationEvent {
    private BigQuestionGroup questionGroup;

    private EventTypeEnum eventType;

    private Long opsUserId;

    public QuestionEvent(Object source, BigQuestionGroup questionGroup, EventTypeEnum eventType, Long opsUserId) {
        super(source);
        this.questionGroup = questionGroup;
        this.eventType = eventType;
        this.opsUserId = opsUserId;
    }

    public BigQuestionGroup getQuestionGroup() {
        return questionGroup;
    }

    public void setQuestionGroup(BigQuestionGroup questionGroup) {
        this.questionGroup = questionGroup;
    }

    public EventTypeEnum getEventType() {
        return eventType;
    }

    public void setEventType(EventTypeEnum eventType) {
        this.eventType = eventType;
    }

    public Long getOpsUserId() {
        return opsUserId;
    }

    public void setOpsUserId(Long opsUserId) {
        this.opsUserId = opsUserId;
    }
}
