package com.unipus.digitalbook.model.events;

import org.springframework.context.ApplicationEvent;

import java.util.List;

public class UserDeleteEvent extends ApplicationEvent {
    private final List<Long> userIds;

    private final Long opUserId;

    public UserDeleteEvent(Object source, List<Long> userIds, Long opUserId) {
        super(source);
        this.userIds = userIds;
        this.opUserId = opUserId;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public Long getOpUserId() {
        return opUserId;
    }
}