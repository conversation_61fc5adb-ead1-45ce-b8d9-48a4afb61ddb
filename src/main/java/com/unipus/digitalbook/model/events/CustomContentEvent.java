package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.enums.CustomContentEventEnum;
import com.unipus.digitalbook.model.po.content.CustomContentPO;

/**
 * 自建内容事件
 */
public class CustomContentEvent {
    
    /**
     * 自建内容业务ID
     */
    private String bizId;

    /**
     * 内容类型  1：自定义章节/2：自定义段落
     */
    private Integer type;

    /**
     * 内容状态  0：编写中/1：待发布/2：已发布
     */
    private Integer status;

    /**
     * 内容数据包
     */
    private String contentPackage;

    /**
     * 数据包ID
     */
    String dataPackage;

    /**
     * 事件类型
     */
    private CustomContentEventEnum event;

    public CustomContentEvent() {
    }

    public CustomContentEvent(CustomContentPO customContentPO, String contentPackage, String dataPackage, CustomContentEventEnum event) {
        this.bizId = customContentPO.getBizId();
        this.type = customContentPO.getType();
        this.status = customContentPO.getStatus();
        this.contentPackage = contentPackage;
        this.dataPackage = dataPackage;
        this.event = event;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getContentPackage() {
        return contentPackage;
    }

    public void setContentPackage(String contentPackage) {
        this.contentPackage = contentPackage;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public CustomContentEventEnum getEvent() {
        return event;
    }

    public void setEvent(CustomContentEventEnum event) {
        this.event = event;
    }
}
