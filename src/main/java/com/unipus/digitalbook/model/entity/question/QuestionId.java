package com.unipus.digitalbook.model.entity.question;

import java.util.Objects;

public class QuestionId {
    private String bizQuestionId;

    private String versionNumber;

    public QuestionId(){}
    public QuestionId(String bizQuestionId, String versionNumber) {
        this.bizQuestionId = bizQuestionId;
        this.versionNumber = versionNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof QuestionId)) return false;
        QuestionId that = (QuestionId) o;
        return Objects.equals(bizQuestionId, that.bizQuestionId) &&
                Objects.equals(versionNumber, that.versionNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bizQuestionId, versionNumber);
    }

    public String getBizQuestionId() {
        return bizQuestionId;
    }

    public void setBizQuestionId(String bizQuestionId) {
        this.bizQuestionId = bizQuestionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }
}
