package com.unipus.digitalbook.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "权限系统-权限数据模型")
public class AuthPermission {

    @Schema(description = "所属controller")
    String controller;
    @Schema(description = "controller描述")
    String controllerDesc;
    @Schema(description = "方法名")
    String methodName;
    @Schema(description = "资源说明")
    String apiDesc;
    @Schema(description = "资源名称")
    private String apiSummary;

    @Schema(description = "资源地址")
    String apiUrl;

    @Schema(description = "请求方式",examples = {"POST","GET","DELETE","PUT"})
    String requestType;

    public String getController() {
        return controller;
    }

    public void setController(String controller) {
        this.controller = controller;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getApiDesc() {
        return apiDesc;
    }

    public void setApiDesc(String apiDesc) {
        this.apiDesc = apiDesc;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getControllerDesc() {
        return controllerDesc;
    }

    public void setControllerDesc(String controllerDesc) {
        this.controllerDesc = controllerDesc;
    }

    public String getApiSummary() {
        return apiSummary;
    }

    public void setApiSummary(String apiSummary) {
        this.apiSummary = apiSummary;
    }
}
