package com.unipus.digitalbook.model.entity.permission;

import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.model.entity.UserInfo;

/**
 * 资源用户信息实体
 */
public class ResourceUser {

    private Long userId;

    private String userName;

    private String mobile;

    private Boolean userValid = Boolean.TRUE;

    public ResourceUser(){}

    public ResourceUser(UserInfo userInfo) {
        this.userId = userInfo.getId();
        this.userName = userInfo.getName();
        this.mobile = UserUtil.desensitization(userInfo.getCellPhone());
        this.userValid = userInfo.getEnable();
    }

    public ResourceUser(Long userId, String userName, String mobile, Boolean userValid) {
        this.userId = userId;
        this.userName = userName;
        this.mobile = UserUtil.desensitization(mobile);
        this.userValid = userValid;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Boolean getUserValid() {
        return userValid;
    }

    public void setUserValid(Boolean userValid) {
        this.userValid = userValid;
    }
}
