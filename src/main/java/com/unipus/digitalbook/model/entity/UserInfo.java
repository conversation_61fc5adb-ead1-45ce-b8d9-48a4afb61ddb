package com.unipus.digitalbook.model.entity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 用户信息实体类
 * 表：user_info
 *
 * <AUTHOR>
 * @date 2024年11月27日 11:13:07
 */
public class UserInfo implements Serializable {

    /**
     * 序列化版本ID
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * SSO ID
     */
    private String ssoId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String cellPhone;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 性别，0:女, 1:男, 2:未知
     */
    private Integer gender;

    /**
     * 简介
     */
    private String desc;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 状态，true:有效, false:无效
     */
    private Boolean enable;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 状态
     * 0:未激活，1:已激活，2:已冻结
     */
    private Integer status;

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置用户ID
     *
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取SSO ID
     *
     * @return SSO ID
     */
    public String getSsoId() {
        return ssoId;
    }

    /**
     * 设置SSO ID
     *
     * @param ssoId SSO ID
     */
    public void setSsoId(String ssoId) {
        this.ssoId = ssoId == null ? null : ssoId.trim();
    }

    /**
     * 获取姓名
     *
     * @return 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置姓名
     *
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 获取手机号
     *
     * @return 手机号
     */
    public String getCellPhone() {
        return cellPhone;
    }

    /**
     * 设置手机号
     *
     * @param cellPhone 手机号
     */
    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone == null ? null : cellPhone.trim();
    }

    /**
     * 获取邮箱地址
     *
     * @return 邮箱地址
     */
    public String getEmail() {
        return email;
    }

    /**
     * 设置邮箱地址
     *
     * @param email 邮箱地址
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * 获取性别
     *
     * @return 性别，0:女, 1:男, 2:未知
     */
    public Integer getGender() {
        return gender;
    }

    /**
     * 设置性别
     *
     * @param gender 性别，0:女, 1:男, 2:未知
     */
    public void setGender(Integer gender) {
        this.gender = gender;
    }

    /**
     * 获取简介
     *
     * @return 简介
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 设置简介
     *
     * @param desc 简介
     */
    public void setDesc(String desc) {
        this.desc = desc == null ? null : desc.trim();
    }

    /**
     * 获取头像地址
     *
     * @return 头像地址
     */
    public String getAvatarUrl() {
        return avatarUrl;
    }

    /**
     * 设置头像地址
     *
     * @param avatarUrl 头像地址
     */
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl == null ? null : avatarUrl.trim();
    }

    /**
     * 获取状态
     *
     * @return 状态，true:有效, false:无效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置状态
     *
     * @param enable 状态，true:有效, false:无效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取最后更新时间
     *
     * @return 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置最后更新时间
     *
     * @param updateTime 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取创建者ID
     *
     * @return 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置创建者ID
     *
     * @param createBy 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取最后更新者ID
     *
     * @return 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置最后更新者ID
     *
     * @param updateBy 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取激活时间
     *
     * @return 激活时间
     */
    public Date getActiveTime() {
        return activeTime;
    }

    /**
     * 设置激活时间
     *
     * @param activeTime 激活时间
     */
    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    /**
     * 转换为字符串
     *
     * @return 字符串表示
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", UserInfo.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("ssoId='" + ssoId + "'")
                .add("name='" + name + "'")
                .add("cellPhone='" + cellPhone + "'")
                .add("email='" + email + "'")
                .add("gender=" + gender)
                .add("desc='" + desc + "'")
                .add("avatarUrl='" + avatarUrl + "'")
                .add("enable=" + enable)
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("activeTime=" + activeTime)
                .add("status=" + status)
                .toString();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}