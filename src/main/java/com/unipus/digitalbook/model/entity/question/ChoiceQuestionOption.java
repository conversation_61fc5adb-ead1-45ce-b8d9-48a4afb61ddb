package com.unipus.digitalbook.model.entity.question;

import java.util.Objects;

/**
 * 题目选项
 */
public class ChoiceQuestionOption {
    private Long id;
    /**
     * 所属题目ID
     */
    private Long questionId;
    /**
     * 选项ID
     */
    private String optionId;

    /**
     * 选项的名字 A B C D
     */
    private String name;

    /**
     * 选项内容
     */
    private String content;

    /**
     * 排序
     */
    private Integer sortOrder;
    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否可用
     */
    private Boolean enable;

    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChoiceQuestionOption that = (ChoiceQuestionOption) o;
        return Objects.equals(optionId, that.optionId) &&
                Objects.equals(name, that.name) &&
                Objects.equals(sortOrder, that.sortOrder) &&
                Objects.equals(content, that.content);

    }
    @Override
    public int hashCode() {
        return Objects.hash(optionId, name, content, sortOrder);
    }
    public String getOptionId() {
        return optionId;
    }

    public void setOptionId(String optionId) {
        this.optionId = optionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "ChoiceQuestionOption{" +
                "optionId='" + optionId + '\'' +
                ", name='" + name + '\'' +
                ", content='" + content + '\'' +
                ", createBy=" + createBy +
                ", updateBy=" + updateBy +
                '}';
    }
}