package com.unipus.digitalbook.model.entity.question.type;

import com.unipus.digitalbook.model.entity.question.IQuestion;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

import java.math.BigDecimal;

/**
 * 多媒体上传题
 */
public class MultiMediaUploadQuestion extends Question implements IQuestion {
    @Override
    public BigDecimal doScore(double accuracy) {
        return BigDecimal.ZERO;
    }

    @Override
    public double judge(UserAnswer userAnswer) {
        return 0;
    }
}
