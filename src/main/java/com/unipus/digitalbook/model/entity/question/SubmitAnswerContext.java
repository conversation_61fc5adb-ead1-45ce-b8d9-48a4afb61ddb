package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.model.enums.ContentTypeEnum;

public class SubmitAnswerContext {
    private Long tenantId;
    private String bookId;

    private String bookVersionNumber;

    private String contentId;

    private Long contentVersionId;

    private String dataPackage;

    private String clientIp;

    private String openId;

    private String envPartition;

    private ContentTypeEnum contentType;


    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public Long getContentVersionId() {
        return contentVersionId;
    }

    public void setContentVersionId(Long contentVersionId) {
        this.contentVersionId = contentVersionId;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public ContentTypeEnum getContentType() {
        return contentType;
    }

    public void setContentType(ContentTypeEnum contentType) {
        this.contentType = contentType;
    }
}
