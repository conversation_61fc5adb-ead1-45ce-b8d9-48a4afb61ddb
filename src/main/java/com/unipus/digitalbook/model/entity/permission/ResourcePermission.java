package com.unipus.digitalbook.model.entity.permission;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 资源权限信息实体
 */
public class ResourcePermission implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源类型 1：教材 2：章节
     */
    private Integer resourceType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 当前权限类型 0x01：共享 0x02：阅读 0x04：编辑 0x08：拥有
     */
    private Integer permissionType;

    /**
     * 变更前权限类型 0x01：共享 0x02：阅读 0x04：编辑 0x08：拥有
     */
    private Integer oldPermissionType;

    /**
     * 是否启用
     */
    private Boolean enable;

    private Date updateTime;


    public ResourcePermission() {}

    public ResourcePermission(String resourceId, Integer resourceType, Long userId, Integer permissionType) {
        this.resourceId = resourceId;
        this.resourceType = resourceType;
        this.userId = userId;
        this.permissionType = permissionType;
        this.oldPermissionType = null;
        this.enable = true;
    }

    public ResourcePermission(String resourceId, Integer resourceType, Long userId, Integer permissionType, Integer oldPermissionType) {
        this.resourceId = resourceId;
        this.resourceType = resourceType;
        this.userId = userId;
        this.permissionType = permissionType;
        this.oldPermissionType = oldPermissionType;
        this.enable = true;
    }

    public Boolean compareResourceAndUser(ResourcePermission other){
        if(other == null){
            return false;
        }else {
            return this.getResourceId().equals(other.getResourceId()) && this.getUserId().equals(other.getUserId());
        }
    }

    public Integer getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(Integer permissionType) {
        this.permissionType = permissionType;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getOldPermissionType() {
        return oldPermissionType;
    }

    public void setOldPermissionType(Integer oldPermissionType) {
        this.oldPermissionType = oldPermissionType;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ResourcePermission that)) {
            return false;
        }
        return Objects.equals(resourceId, that.resourceId) &&
                Objects.equals(resourceType, that.resourceType) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(permissionType, that.permissionType) &&
                Objects.equals(oldPermissionType, that.oldPermissionType) &&
                Objects.equals(enable, that.enable);
    }

}
