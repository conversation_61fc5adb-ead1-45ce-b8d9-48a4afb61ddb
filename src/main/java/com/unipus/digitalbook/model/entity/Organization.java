package com.unipus.digitalbook.model.entity;

import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * 组织实体类
 */
@Getter
public class Organization {
    /**
     * 组织ID
     */
    private Long id;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 组织类型
     */
    private Integer orgType;

    /**
     * 父组织ID
     */
    private Long parentId;

    /**
     * 父级机构名称
    */
    private String parentName;

    /**
     * 父级机构路径
    */
    private String parentPath;

    /**
     * 组织状态（例如：启用、禁用）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 组织层级
     */
    private Integer level;

    /**
     * 子机构列表
     */
    private List<Organization> subOrgList;

    /**
     * 启用机构
     */
    public void enable() {
        this.enable = true;
    }

    /**
     * 禁用机构的方法
     */
    public void disable() {
        this.enable = false;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Organization setSubOrgList(List<Organization> subOrgList) {
        this.subOrgList = subOrgList;
        return this;
    }

    public Organization setParentPath(String parentPath) {
        this.parentPath = parentPath;
        return this;
    }

}

