package com.unipus.digitalbook.service.converter.tenant;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.common.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.time.Instant;

/**
 * uai同步信息转换成统一结构
 * 非标准结构，需要转成统一结构
 */
@Component("syncUaiAnswerResponseConverter")
@Slf4j
public class SyncUaiAnswerResponseConverter implements BodyConverter<String, Response<String>> {
    @Override
    public Response<String> convert(String source) throws Exception {
        if (!StringUtils.hasText(source)) {
            throw new IllegalStateException("同步作答为没有响应");
        }
        UaiResponse uaiResponse = JsonUtil.parseObject(source, UaiResponse.class);
        if (uaiResponse == null || uaiResponse.getData() == null) {
            throw new IllegalStateException("同步作答失败");
        }
        StrategyNode strategyNode = uaiResponse.getData().getStrategyNode();
        if(uaiResponse.getData().isPass() || strategyNode == null) {
            return Response.success(source);
        }
        // 先判断是否在单元有效期内
        if (strategyNode.getStartTime() != 0 && strategyNode.getEndTime() != 0) {
            long nowEpochSecond = Instant.now().getEpochSecond();
            if (strategyNode.getStartTime() > nowEpochSecond || strategyNode.getEndTime() < nowEpochSecond) {
                String startDateStr = DateUtil.dateTimeFormatYMD(strategyNode.getStartTime());
                String endDateStr = DateUtil.dateTimeFormatYMD(strategyNode.getEndTime());
                String errorMessage = MessageFormat.format("非单元有效期学习成绩不统计，请在单元有效期{0}～{1}内学习", startDateStr, endDateStr);
                log.warn("单元有效期学习成绩不统计: {}", errorMessage);
                return Response.fail(errorMessage);
            }
        }
        if (strategyNode.getTask_mini_score_pct() > 0) {
            String message = MessageFormat.format("当前作答客观题正确率未达到{0}%, 请检查作答内容", strategyNode.getTask_mini_score_pct()*100);
            return Response.fail(message);
        }
        return Response.success(source);
    }

    public static class UaiResponse{

        private String message;

        private UaiResponseData data;

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public UaiResponseData getData() {
            return data;
        }

        public void setData(UaiResponseData data) {
            this.data = data;
        }
    }
    public static class UaiResponseData {
        private String strategy;
        private int state;

        public String getStrategy() {
            return strategy;
        }

        public void setStrategy(String strategy) {
            this.strategy = strategy;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }

        public boolean isPass() {
            return state == 1;
        }
        public StrategyNode getStrategyNode() {
            if (!StringUtils.hasText(strategy)) {
                return null;
            }
            return JsonUtil.parseObject(strategy, StrategyNode.class);
        }
    }
    public static class StrategyNode {
        private long endTime;
        private long startTime;
        private boolean required;

        private double task_mini_score_pct;

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public boolean isRequired() {
            return required;
        }

        public void setRequired(boolean required) {
            this.required = required;
        }

        public double getTask_mini_score_pct() {
            return task_mini_score_pct;
        }

        public void setTask_mini_score_pct(double task_mini_score_pct) {
            this.task_mini_score_pct = task_mini_score_pct;
        }
    }
}
