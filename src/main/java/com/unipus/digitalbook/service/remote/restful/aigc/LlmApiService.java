package com.unipus.digitalbook.service.remote.restful.aigc;

import com.unipus.digitalbook.service.remote.restful.aigc.model.AigcApiResponse;
import com.unipus.digitalbook.service.remote.restful.aigc.model.ChatCompletionRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange("/v1")
public interface LlmApiService {

    @PostExchange("/chat/completions")
    AigcApiResponse getChatCompletion(
            @RequestHeader("Authorization") String authorization,
            @RequestBody ChatCompletionRequest request
    );
}
