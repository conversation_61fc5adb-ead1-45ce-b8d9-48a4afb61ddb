package com.unipus.digitalbook.service.remote.restful.engine;

import com.google.common.net.HttpHeaders;
import com.unipus.digitalbook.service.remote.restful.BaseApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 引擎的接口配置类
 */
@Slf4j
@Configuration
public class EngineApiConfig extends BaseApiConfig {

    @Value("${remote.engine.host}")
    private String engineHost;
    @Value("${remote.engine.appKey}")
    private String appKey;
    @Value("${remote.engine.appSecret}")
    private String appSecret;

    @Bean
    public EngineApiService engineApiService(WebClient.Builder webClientBuilder) {
        // 生成 Base64 认证信息
        String authHeader = generateAuthHeader(appKey, appSecret);
        // 创建 WebClient 并添加认证请求头
        WebClient webClient = webClientBuilder
                .baseUrl(engineHost)
                .defaultHeader(HttpHeaders.AUTHORIZATION, authHeader) // 添加 Authorization 头
                .build();

        // 创建 WebClientAdapter
        WebClientAdapter adapter = createWebClientAdapter(webClient);

        // 创建 HTTP 代理工厂并返回 EngineApiService 实例
        return createHttpServiceProxyFactory(adapter).createClient(EngineApiService.class);
    }

    /**
     * 生成 Base64 认证头："Basic base64(appKey:appSecret)"
     */
    private String generateAuthHeader(String appKey, String appSecret) {
        String credentials = appKey + ":" + appSecret;
        String base64Credentials = Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
        return "Basic" + base64Credentials;
    }
}
