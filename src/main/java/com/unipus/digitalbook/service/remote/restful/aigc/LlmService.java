package com.unipus.digitalbook.service.remote.restful.aigc;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.service.remote.restful.aigc.model.AigcApiResponse;
import com.unipus.digitalbook.service.remote.restful.aigc.model.ChatCompletionRequest;
import com.unipus.digitalbook.service.remote.restful.aigc.model.ChatRequestBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class LlmService {
    @Value("${remote.aigc.sk}")
    String sk;

    private final LlmApiService llmApiService;

    public AigcApiResponse processImagePrompt(String prompt, String imageUrl) {
        ChatCompletionRequest request = ChatRequestBuilder.buildImageRequest(prompt, imageUrl);
        return llmApiService.getChatCompletion(
                "Bearer " + sk,
                request
        );
    }

    public AigcApiResponse processImagePrompt(String systemRole, String prompt, String imageUrl) {
        ChatCompletionRequest request = ChatRequestBuilder.buildRequest(systemRole, prompt, imageUrl);
        log.debug("llm request: {}", JSON.toJSONString(request) );
        return llmApiService.getChatCompletion(
                "Bearer " + sk,
                request
        );
    }
}