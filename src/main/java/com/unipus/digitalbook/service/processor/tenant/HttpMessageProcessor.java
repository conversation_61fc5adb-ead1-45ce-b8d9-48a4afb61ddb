package com.unipus.digitalbook.service.processor.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Maps;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.UrlUtil;
import com.unipus.digitalbook.conf.tenant.TenantSubscribeConfig;
import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class HttpMessageProcessor extends BaseMessageProcessor {

    @Resource
    private TenantSubscribeConfig tenantSubscribeConfig;

    @Override
    public ChannelEnum supportChannel() {
        return ChannelEnum.HTTP;
    }

    @Override
    @SneakyThrows
    public <S,T> T process(String messageUuid, TenantSubscribePO tenantSubscribePO, TenantChannelPO tenantChannelPO,
                                                  S message, TypeReference<T> targetType) {

        Long tenantId = tenantSubscribePO.getTenantId();
        String messageTopic = tenantSubscribePO.getMessageTopic();
        String callbackUrl = tenantSubscribePO.getHttpUrl();

        String host = UrlUtil.getDomainFromUrl(callbackUrl);
        String uri = callbackUrl.replaceFirst(host, "");
        WebClient webClient = tenantSubscribeConfig.webClient(host);

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");

        String httpHeaderGenerator = tenantChannelPO.getHeaderGenerator();
        if (StringUtils.hasText(httpHeaderGenerator)) {
            Optional.ofNullable(generate(httpHeaderGenerator)).ifPresent(headers::putAll);
        }

        try {
            String httpMethod = tenantChannelPO.getHttpMethod();
            String response = null;
            if ("get".equalsIgnoreCase(httpMethod)) {
                Map<String, String> requestBody = Maps.newHashMap();
                Map<String, String> map = convert(tenantChannelPO.getRequestBodyConverter(), message, new TypeReference<>() {});
                Optional.ofNullable(map).ifPresent(requestBody::putAll);
                response = webClient.get()
                        .uri(uriBuilder -> {
                            uriBuilder.path(uri);
                            requestBody.forEach(uriBuilder::queryParam);
                            return uriBuilder.build(requestBody);
                        }) // 指定 URI
                        .headers(httpHeaders -> headers.forEach(httpHeaders::add)) // 设置请求头
                        .retrieve() // 获取响应
                        .bodyToMono(String.class) // 将响应转换为字符串
                        .block(); // 阻塞，等待响应完成
                log.info("tenant: {} messageUuid: {} messageTopic: {} 消息体: {} 响应结果: {}", tenantId, messageUuid, messageTopic, JsonUtil.toJsonString(requestBody), response);
            } else if ("post".equalsIgnoreCase(httpMethod)) {
                String requestBody = convert(tenantChannelPO.getRequestBodyConverter(), message, new TypeReference<>() {});
                response = webClient.post()
                        .uri(uri) // 指定 URI
                        .headers(httpHeaders -> headers.forEach(httpHeaders::add)) // 设置请求头
                        .bodyValue(requestBody) // 设置请求体
                        .retrieve() // 获取响应
                        .bodyToMono(String.class) // 将响应转换为字符串
                        .block(); // 阻塞，等待响应完成
                log.info("tenant: {} messageUuid: {} messageTopic: {} 消息体: {} 响应结果: {}", tenantId, messageUuid, messageTopic, requestBody, response);
            } else {
                throw new TenantMessageException("不支持的 HTTP 方法: " + httpMethod);
            }

            if (response == null) {
                throw new TenantMessageException(ProduceResultEnum.FAIL.getMessage());
            }

            if (targetType.getType().equals(Void.class)) {
                return null;
            }

            return convert(tenantChannelPO.getResponseBodyConverter(), response, targetType);
        } catch (Exception e) {
            log.error("tenantId: {} messageUuid: {} messageTopic: {} 消息体: {}", tenantId, messageUuid, messageTopic, JsonUtil.toJsonString(message), e);
            if (e instanceof TenantMessageException) {
                throw e;
            }
            throw new TenantMessageException(e);
        }
    }
}
