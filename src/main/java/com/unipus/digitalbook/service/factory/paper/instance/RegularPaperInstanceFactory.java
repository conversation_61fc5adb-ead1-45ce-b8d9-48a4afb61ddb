package com.unipus.digitalbook.service.factory.paper.instance;

import com.unipus.digitalbook.dao.PaperRoundPOMapper;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import com.unipus.digitalbook.service.PaperService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 常规卷实例工厂
 */
@Service
@Slf4j
public class RegularPaperInstanceFactory extends AbstractPaperInstanceFactory {

    @Resource
    private PaperService paperService;
    @Resource
    private PaperRoundPOMapper paperRoundPOMapper;

    /**
     * 获取试卷类型
     * @return 试卷类型
     */
    @Override
    public PaperTypeEnum getPaperType() {
        return PaperTypeEnum.REGULAR;
    }

    /**
     * 创建预览模式试卷
     * @param paper 试卷对象
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 试卷模式
     * @return 试卷实例对象
     */
    @Override
    public PaperInstance createPreviewPaperInstance(Paper paper, String openId, Long tenantId, UnitTestModeEnum testMode) {
        String paperId = paper.getPaperId();
        String versionNumber = paper.getVersionNumber();
        // 获取试卷题目组
        List<BigQuestionGroup> finalQuestionList = paperService.getQuestions(paperId, versionNumber);
        // 生成预览模式试卷
        return super.createPaperInstance(paper, finalQuestionList, generateScoreBatchId(), generateInstanceId(), openId, tenantId, testMode);
    }

    /**
     * 创建正式模式试卷
     * @param paper 试卷对象
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 试卷模式
     * @return 试卷实例对象
     */
    @Override
    public PaperInstance createRealPaperInstance(Paper paper, String openId, Long tenantId, UnitTestModeEnum testMode) {
        List<BigQuestionGroup> questions = generateRegularQuestionList(paper);
        return super.createPaperInstance(paper, questions, super.generateScoreBatchId(), null, openId, tenantId, testMode);
    }

    /**
     * 生成常规卷题目列表
     * @param paper 试卷参数
     * @return 试卷题目列表
     */
    private List<BigQuestionGroup> generateRegularQuestionList(Paper paper) {
        String paperId = paper.getPaperId();
        return  paperService.getQuestions(paperId, paper.getVersionNumber());
    }

    /**
     * 获取最近一次试卷实例信息
     * @param paperId 试卷ID
     * @param paperVersion 版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 诊断卷测试模式(1:诊断模式/2:推荐模式)
     * @param submitStatus 提交状态
     * @return 最近一次试卷实例信息
     */
    @Override
    protected PaperRoundPO getLatestInstanceInfo(String paperId, String paperVersion, String openId, Long tenantId, UnitTestModeEnum testMode, Integer submitStatus){
        // 查询最新一次试卷提交记录
        return paperRoundPOMapper.getLatestPaperInstance(paperId, null, openId, tenantId, submitStatus);
    }
}