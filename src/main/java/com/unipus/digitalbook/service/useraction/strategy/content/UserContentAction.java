package com.unipus.digitalbook.service.useraction.strategy.content;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;

import java.util.Set;

public interface UserContentAction {
    Set<ContentTypeEnum> getTypes();

    /**
     * 节点完成
     * @param userAction
     * @return
     */
    Response<Boolean> finishNode(UserAction userAction);

    /**
     * 完成题目节点
     * @param userAction
     * @return
     */
    Response<Boolean> finishQuestionNode(UserAction userAction);

    /**
     * 节点完成后置处理
     * 比如 持久化等操作
     * @param userAction
     * @return
     */
    Response<Boolean> postProcessNode(UserAction userAction, ContentNode contentNode);

    /**
     * 内容发布 后置处理流程
     * @param tenantId 租户id
     * @param contentId 发布的内容id
     * @param contentVersionId 发布的内容版本ID
     * @return
     */
    Response<Boolean> postPublishProcess(Long tenantId, String contentId, Long contentVersionId);


    default boolean isCompletedNode(byte[] bytes, Integer offset) {
        if(bytes == null) {
            return false;
        }
        int byteIndex = offset / 8;
        int bitInByte = 7 - (offset % 8);
        return byteIndex < bytes.length && (bytes[byteIndex] & (1 << bitInByte)) != 0;
    }
}
