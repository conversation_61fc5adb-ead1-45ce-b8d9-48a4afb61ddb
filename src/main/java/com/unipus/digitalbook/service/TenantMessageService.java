package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.po.tenant.TenantMessagePO;

import java.util.List;

public interface TenantMessageService {

    void insertMessage(TenantMessagePO tenantMessagePO);

    TenantMessagePO selectById(Long id);

    List<TenantMessagePO> selectByTenantIdAndMessageTopicLimitFromId(Long tenantId, String messageTopic, Long id, Integer limit);

    void deleteById(Long id);
}
