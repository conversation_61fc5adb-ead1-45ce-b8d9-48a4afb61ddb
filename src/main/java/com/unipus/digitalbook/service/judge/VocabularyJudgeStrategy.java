package com.unipus.digitalbook.service.judge;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.clio.ClioSentResult;
import com.unipus.digitalbook.model.entity.question.type.VocabularyQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Set;

/**
 * 单词本评测策略
 *
 * <AUTHOR>
 * @date 2025/3/21 14:41
 */
@Slf4j
@Component
public class VocabularyJudgeStrategy extends AbstractOralJudgeStrategy<VocabularyQuestion> {

    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.VOCABULARY);
    }

    @Override
    protected BigDecimal total(String evaluation) {
        ClioSentResult clioSentResult = JSON.parseObject(evaluation, ClioSentResult.class);
        if (clioSentResult != null && clioSentResult.getFinalResult() != null
                && clioSentResult.getFinalResult().getResult() != null
                && clioSentResult.getFinalResult().getResult().getTotal() != null) {
            return BigDecimal.valueOf(clioSentResult.getFinalResult().getResult().getTotal());
        }
        log.error("单词本作答结果数据异常: {}", evaluation);
        return BigDecimal.ZERO;
    }
}
