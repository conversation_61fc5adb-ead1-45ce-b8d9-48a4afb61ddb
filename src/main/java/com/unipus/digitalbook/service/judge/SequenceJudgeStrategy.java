package com.unipus.digitalbook.service.judge;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.type.SequenceQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class SequenceJudgeStrategy implements JudgeStrategy<SequenceQuestion> {
    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.SEQUENCE);
    }

    @Override
    public double judge(SequenceQuestion question, UserAnswer userAnswer) {
        String answer = userAnswer.getAnswer();
        List<String> optionIds = JsonUtil.parseObject(answer, new TypeReference<>() {
        });
        List<QuestionAnswer> answers = question.getAnswers();
        int size = answers.size();
        int correctCount = 0;
        for (int i = 0; i < answers.size(); i++) {
            if (i >= optionIds.size()) {
                break;
            }
            if (answers.get(i).getCorrectAnswerText().equals(optionIds.get(i))) {
                correctCount++;
            }
        }
        return (double) correctCount / size;
    }
}
