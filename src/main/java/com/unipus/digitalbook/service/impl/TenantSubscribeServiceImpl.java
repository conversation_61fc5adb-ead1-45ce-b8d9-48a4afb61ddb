package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.TenantSubscribePOMapper;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.service.TenantSubscribeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TenantSubscribeServiceImpl implements TenantSubscribeService {

    private final TenantSubscribePOMapper tenantSubscribePOMapper;

    @Autowired
    public TenantSubscribeServiceImpl(TenantSubscribePOMapper tenantSubscribePOMapper) {
        this.tenantSubscribePOMapper = tenantSubscribePOMapper;
    }

    @Override
    public TenantSubscribePO selectByTenantIdAndMessageTopic(Long tenantId, String messageTopic) {
        return tenantSubscribePOMapper.selectByTenantIdAndMessageTopic(tenantId, messageTopic);
    }

    @Override
    public List<TenantSubscribePO> selectAll() {
        return tenantSubscribePOMapper.selectAll();
    }
}
