package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.org.OrgListEmptyException;
import com.unipus.digitalbook.common.utils.RedisUtil;
import com.unipus.digitalbook.dao.OrgUserRelationPOMapper;
import com.unipus.digitalbook.dao.OrganizationPOMapper;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.constants.CommonConstant;
import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.enums.OrgStatusEnum;
import com.unipus.digitalbook.model.po.OrgUserRelationPO;
import com.unipus.digitalbook.model.po.OrganizationPO;
import com.unipus.digitalbook.model.po.User2OrgPO;
import com.unipus.digitalbook.publisher.standalone.UserEventPublisher;
import com.unipus.digitalbook.service.OrgService;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 组织服务实现类
 */
@Service
@Slf4j
public class OrgServiceImpl implements OrgService {
    @Resource
    private OrganizationPOMapper organizationMapper;
    @Resource
    private OrgUserRelationPOMapper relationPOMapper;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserEventPublisher userEventPublisher;

    @Override
    public Boolean userExistInOrg(Long userId, Long orgId) {
        return relationPOMapper.userExistInOrg(userId, orgId) > 0;
    }

    @Override
    public Boolean existById(Long id) {
        OrganizationPO po = organizationMapper.selectByPrimaryKey(id);
        return po != null && po.getEnable();
    }

    /**
     * 取得组织树
     * @param parentOrgId 指定父级组织ID（作为组织树的根节点）
     * @param subLevel 子节点层级数量(0:全部，1:一级子节点，2:二级子节点，以此类推)
     * @param status 状态，1:启用，0:禁用
     * @return 机构列表
     */
    @Override
    public List<Organization> getOrganizationTree(Long parentOrgId, Integer subLevel, Integer status) {
        List<Organization> organizations;

        // 尝试从Redis缓存中获取组织树
        organizations = redisUtil.readListFromBucket(CacheConstant.REDIS_ORG_KEY, Organization.class);
        if(CollectionUtils.isEmpty(organizations)){
            // 从数据库查询组织信息并刷新缓存
            organizations = refreshOrgCache();
        }

        // 根据组织ID过滤组织树
        return filterOrgTree(organizations, parentOrgId, subLevel, status);
    }

    /**
     * 深度优先搜索子组织树
     * @param organizations 组织树
     * @param parentOrgId 指定父级组织ID（作为组织树的根节点）
     * @param subLevel 子节点层级数
     * @param status 状态
     * @return 子组织树
     */
    private List<Organization> filterOrgTree(List<Organization> organizations, Long parentOrgId, Integer subLevel, Integer status) {
        if(CollectionUtils.isEmpty(organizations) || parentOrgId == null || parentOrgId <= 0){
            // 无组织树或未指定根组织（按照指定层级进行裁剪处理）
            return trimOrgTree(organizations, subLevel, status);
        }
        for(Organization org : organizations){
            if(parentOrgId.equals(org.getId())){
                // 找到相对根组织，保留指定层级的下级组织
                return trimOrgTree(org.getSubOrgList(), subLevel, status);
            }else{
                List<Organization> subOrgList = filterOrgTree(org.getSubOrgList(), parentOrgId, subLevel, status);
                if(!CollectionUtils.isEmpty(subOrgList)){
                    return subOrgList;
                }
            }
        }
        return Collections.emptyList();
    }

    /**
     * 递归处理子节点返回指定层级的子节点
     * @param subOrgList 子节点列表
     * @param subLevel 子节点层级数
     * @param status 状态（1：启用/0：禁用/null：未指定）
     * @return 子节点列表
     */
    private List<Organization> trimOrgTree(List<Organization> subOrgList, Integer subLevel, Integer status) {
        if(CollectionUtils.isEmpty(subOrgList)){
            // 无子节点
            return Collections.emptyList();
        }
        List<Organization> removableOrgList = new ArrayList<>();
        for(Organization org : subOrgList){
            if(status!=null && !status.equals(org.getStatus())){
                // 忽略指定状态以外的子节点
                removableOrgList.add(org);
                continue;
            }
            if(subLevel==null || subLevel == 0) {
                // 递归处理子节点（仅状态筛选）
                org.setSubOrgList(trimOrgTree(org.getSubOrgList(), null, status));
            } else
            if(subLevel > 1){
                // 递归处理子节点（裁剪和状态筛选）
                org.setSubOrgList(trimOrgTree(org.getSubOrgList(), subLevel - 1, status));
            }else{
                // 清除子节点
                org.setSubOrgList(null);
            }
        }
        subOrgList.removeAll(removableOrgList);
        return subOrgList;
    }

    /**
     * 获取组织树并刷新缓存
     *
     * @return 组织树列表
     */
    private List<Organization>  refreshOrgCache() {

        // 从数据库查询组织信息（排序字段：上级组织ID/创建时间/组织名称）
        List<OrganizationPO> organizationPOList = organizationMapper.selectAllByCreateTimeDesc();
        if (CollectionUtils.isEmpty(organizationPOList)) {
            throw new OrgListEmptyException();
        }

        // 重新构建组织树
        List<Organization> organizations = buildOrgEntityTree(organizationPOList);

        // 将组织树缓存到Redis
        redisUtil.writeListToBucket(CacheConstant.REDIS_ORG_KEY, organizations, CacheConstant.REDIS_TIMEOUT_SECONDS);
        return organizations;
    }

    /**
     * 主动清除组织树缓存
     */
    private void clearOrgCache() {
        redisUtil.deleteFromBucket(CacheConstant.REDIS_ORG_KEY);
    }

    /**
     * 构建组织树
     * @param organizationPOList : 组织列表
     * @return 机构列表
     */
    private List<Organization> buildOrgEntityTree(List<OrganizationPO> organizationPOList) {
        List<Organization> orgEntityList = new ArrayList<>();

        // 缓存Entity到Map
        Map<Long, Organization> orgMap = new HashMap<>();
        for (OrganizationPO org : organizationPOList) {
            orgMap.put(org.getId(), org.toEntity());
        }

        // 构建组织树
        for (OrganizationPO po : organizationPOList) {
            // 取得组织实体对象
            Organization entity = orgMap.get(po.getId());
            if (entity.getParentId() == 0 || !orgMap.containsKey(entity.getParentId())) {
                // 保存根机构
                orgEntityList.add(entity);
                // 无需处理上级组织
                continue;
            }
            // 获取父机构
            Organization parentEnt = orgMap.get(entity.getParentId());
            // 设置父机构名称
            entity.setParentName(parentEnt.getOrgName());
            // 设置父机构的子机构列表
            if (parentEnt.getSubOrgList() == null) { parentEnt.setSubOrgList(new ArrayList<>()); }
            parentEnt.getSubOrgList().add(entity);
        }

        // 构建组织路径（由于不能保证组织的顺序，需单独构建组织路径）
        buildOrgPath(orgEntityList, 0, null);

        return orgEntityList;
    }

    /**
     * 构建组织路径
     * @param orgEntityList : 组织列表
     * @param level   层级
     * @param parentPath 父机构路径
     */
    private void buildOrgPath(List<Organization> orgEntityList, int level, String parentPath) {
        if(CollectionUtils.isEmpty(orgEntityList)){
            return;
        }
        // 设置当前机构的层级
        level += 1;
        // 设置当前机构的父机构路径
        for(Organization org : orgEntityList){
            org.setLevel(level);
            org.setParentPath(parentPath);

            List<Organization> subOrgList = org.getSubOrgList();
            if(!CollectionUtils.isEmpty(subOrgList)){
                String path = (StringUtils.hasText(parentPath)? parentPath + CommonConstant.PATH_SEPARATOR:"") + org.getOrgName();
                buildOrgPath(subOrgList, level, path);
            }
        }
    }

    /**
     * 查询符合条件的所有机构
     * @param name 机构名称
     * @param status 机构状态
     * @param offset 偏移量
     * @param limit  每页数量
     * @return 机构列表
     */
    @Override
    public List<Organization> searchOrganizations(String name, Integer status, int offset, int limit){
        return organizationMapper.searchByOrgNameWithPath(name, status, offset, limit,useLike(name));
    }

    /**
     * 查询符合条件的所有机构的数量
     * @param name 机构名称
     * @param status 机构状态
     * @return 机构数量
     */
    @Override
    public Integer countOrganizations(String name, Integer status){

        return organizationMapper.countByOrgNameAndStatus(name, status,useLike(name));
    }

    private boolean useLike(String searchTerm){
        boolean useLikeSearch = false;
        if (searchTerm != null && !searchTerm.isEmpty()) {
            // 获取第一个字符
            char firstChar = searchTerm.charAt(0);
            // 判断是否需要使用LIKE搜索:
            // 1. 以a或i开头
            // 2. 第一个字符不是字母、数字
            useLikeSearch = firstChar == 'a' || firstChar == 'i' || firstChar == 'A' || firstChar == 'I' ||
                    (!Character.isLetterOrDigit(firstChar));
        }
        return useLikeSearch;
    }

    /**
     * 查找机构的根机构ID
     *
     * @param orgId 机构ID
     * @return 根机构ID
     */
    private Long findRootOrganizationId(Long orgId) {
        OrganizationPO currentOrg = organizationMapper.selectByPrimaryKey(orgId);
        while (currentOrg != null && currentOrg.getParentId() != null) {
            currentOrg = organizationMapper.selectByPrimaryKey(currentOrg.getParentId());
        }
        return currentOrg != null ? currentOrg.getId() : orgId;
    }

    /**
     * 新建一级机构
     * 超管的系统管理中，新建根节点的机构
     *
     * @param organization    新建一级机构参数
     * @param opUserId 操作用户ID
     * @return 操作是否成功
     */
    @Override
    public Boolean addTopOrganization(Organization organization, Long opUserId) {

         Long orgId =organizationMapper.searchByOrgName(organization.getOrgName());
         if(orgId!=null){
             throw new IllegalArgumentException("机构名称重复");
         }
        // 创建 OrganizationPO 对象并设置属性
        OrganizationPO po = new OrganizationPO();
        po.fromEntity(organization);
        po.setParentId(0L);
        po.setCreateBy(opUserId);

        // 插入数据库
        boolean result = organizationMapper.insertSelective(po)>0;
        if(result) {
            clearOrgCache();
        }
        return result;
    }

    /**
     * 新建下级机构
     * 超管的系统管理中，新建非一级机构
     *
     * @param param    新建下级机构参数
     * @param opUserId 操作用户ID
     * @return 操作是否成功 : 插入成功，返回 true，否则返回 false
     */
    @Override
    public Boolean addSubOrganization(Organization param, Long opUserId) {
        // 检查同名机构是否存在
        Long orgId = organizationMapper.searchByOrgName(param.getOrgName());
        if(orgId!=null){
            throw new IllegalArgumentException("机构名称重复");
        }
        OrganizationPO parentOrg = organizationMapper.selectByPrimaryKey(param.getParentId());
        if(parentOrg==null){
            throw new NoSuchElementException("上级机构不存在");
        }
        if (OrgStatusEnum.INACTIVE.getCode().equals(parentOrg.getStatus())
                && OrgStatusEnum.ACTIVE.getCode().equals(param.getStatus())) {
            throw new IllegalStateException("上级机构状态为禁用，新增的下级机构状态只能设置禁用。");
        }

        // 创建一个新的 OrganizationPO 对象
        OrganizationPO po = new OrganizationPO();
        po.fromEntity(param);
        po.setCreateBy(opUserId);

        // 插入新的组织记录到数据库
        boolean result = organizationMapper.insertSelective(po)>0;
        if(result) {
            clearOrgCache();
        }
        return result;
    }

    /**
     * 编辑机构
     * 超管的系统管理中，编辑机构，涉及到移动组织操作
     *
     * @param param    编辑机构参数
     * @param opUserId 操作用户ID
     * @return 操作是否成功
     */
    @Override
    public Boolean editOrganization(Organization param, Long opUserId) {
        if (opUserId==null){
            throw new IllegalArgumentException("未能获取操作人信息，操作不合法。");
        }
        // 检查同名机构是否存在
        Long orgId = organizationMapper.searchByOrgName(param.getOrgName());
        if(orgId!=null&&!orgId.equals(param.getId())){
            throw new IllegalArgumentException("机构名称重复");
        }
        // 创建一个新的 OrganizationPO 对象
        OrganizationPO po = new OrganizationPO();
        po.fromEntity(param);
        po.setUpdateBy(opUserId);
        // 更新数据库
        boolean result = organizationMapper.updateByPrimaryKeySelective(po)>0;// 如果更新成功，清除组织树缓存
        if(result) {
            clearOrgCache();
        }
        return result;
    }

    /**
     * 批量变更机构状态
     * <p>
     * 在超管的系统管理中，此方法用于批量变更指定机构的状态。
     *
     * @param idList   需要变更状态的机构ID列表
     * @param status   目标状态
     * @param opUserId 操作用户的ID
     * @return 操作是否成功
     */
    @Override
    public Boolean batchChangeOrganizationStatus(List<Long> idList, Integer status, Long opUserId) {
        if (OrgStatusEnum.INACTIVE.getCode().equals(status)){
            //处理禁用逻辑
           Integer subOrgEnableCount= organizationMapper.checkSubOrgEnableStatus(idList);
           if (subOrgEnableCount>0){
               throw new IllegalStateException("该机构的下级机构中存在启用状态的机构，不可禁用当前机构。");
           }
        }else {
        //处理启用逻辑
            Integer parentOrgDisableCount= organizationMapper.checkParentOrgDisableStatus(idList);
            if (parentOrgDisableCount>0){
                throw new IllegalStateException("该机构的上级机构状态为禁用，不可启用当前机构。");
            }
        }
        boolean result = organizationMapper.batchChangeOrganizationStatus(idList, status, opUserId)>0;
        if(result) {
            clearOrgCache();
        }
        return result;
    }

    /**
     * 用户加入组织
     *
     * @param userId   用户ID
     * @param orgId    组织ID
     * @param opUserId 操作用户ID（可能是管理员或其他有权操作的用户）
     * @return 如果用户成功加入组织，返回true；否则返回false
     */
    @Override
    public Boolean joinOrganization(Long userId, Long orgId, Long opUserId) {
        // 根据机构ID查询机构信息
        OrganizationPO po = organizationMapper.selectByPrimaryKey(orgId);
        // 如果机构不存在，抛出非法参数异常
        if (po == null) {
            throw new IllegalArgumentException("机构不存在");
        }
        // 如果机构已被删除，抛出非法参数异常
        if (Boolean.FALSE.equals(po.getEnable())) {
            throw new IllegalStateException("机构已删除");
        }
        // 如果机构状态为禁用，抛出非法参数异常
        if (po.getStatus() != 1) {
            throw new IllegalStateException("机构已禁用");
        }
        // 构建机构用户关系对象，设置加入时间为当前时间，机构ID和用户ID，并将状态设为正常
        OrgUserRelationPO relationPO = new OrgUserRelationPO.Builder()
                .joinTime(new Date())
                .orgId(orgId)
                .userId(userId)
                .status(1)
                .createBy(opUserId)
                .build();
        // 插入机构用户关系记录到数据库
        boolean result = relationPOMapper.insertSelective(relationPO)>0;
        if(result) {
            // 清除机构缓存
            clearOrgCache();
        }
        // 返回插入操作是否成功的布尔值
        return result;
    }

    /**
     * 取得组织详情
     * @param orgId 组织ID
     * @return 组织详情
     */
    @Override
    @Nullable
    public Organization getOrgDetail(Long orgId){
        OrganizationPO organizationPO = organizationMapper.selectByPrimaryKey(orgId);
        if(organizationPO == null || Boolean.FALSE.equals(organizationPO.getEnable())) {
            return null;
        }
        return organizationPO.toEntity();
    }

    /**
     * 根据用户ID获取用户所属的有效的组织ID集合
     * @param userId 用户ID
     * @return 用户所属的组织ID集合
     */
    @Override
    public List<Organization> getUserOrgs(Long userId) {
        List<User2OrgPO> userOrganizations = organizationMapper.getUserOrganizations(List.of(userId), null);
        if(CollectionUtils.isEmpty(userOrganizations)){
            return Collections.emptyList();
        }
        List<OrganizationPO> organizationPOs = userOrganizations.getFirst().getOrganizationList();
        if(CollectionUtils.isEmpty(organizationPOs)){
            return Collections.emptyList();
        }
        return organizationPOs.stream().map(OrganizationPO::toEntity).toList();
    }

    /**
     * 批量删除用户和组织关系
     *
     * @param userIdList 用户ID列表
     * @param orgId      组织ID
     * @param opUserId   操作者ID
     * @return 返回布尔值，指示操作是否成功
     */
    @Override
    public Boolean batchDeleteRelationsByUserIdListAndOrgId(List<Long> userIdList, Long orgId, Long opUserId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return false;
        }
        if (orgId == null || orgId <= 0) {
            return false;
        }

        int count = relationPOMapper.batchDeleteRelationsByUserIdListAndOrgId(userIdList, orgId, opUserId);
        //todo 删除用户与角色关系
        List<Long> haveOtherOrgUserIds =  relationPOMapper.queryValidOrgUserIdsByUserIds(userIdList);
        if (!CollectionUtils.isEmpty(haveOtherOrgUserIds)){
            //找到不存在其他组织的用户id
            List<Long> deleteUserIds = userIdList.stream().filter(userId -> !haveOtherOrgUserIds.contains(userId)).toList();
            if (!CollectionUtils.isEmpty(deleteUserIds)){
                //删除用户
                userEventPublisher.publishUserDeleteEvent(deleteUserIds,opUserId);
            }
        }else {
            userEventPublisher.publishUserDeleteEvent(userIdList,opUserId);
        }
        return count > 0;
    }

    /**
     * 根据用户ID列表获取用户到组织的映射关系
     *
     * @param userIdList 用户ID列表
     * @return 返回一个Map，键为用户ID，值为该用户所属的组织列表
     */
    @Override
    public Map<Long, List<Organization>> getUser2OrgMap(List<Long> userIdList,Long orgId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        List<User2OrgPO> user2OrgPOList = organizationMapper.getUserOrganizations(userIdList,orgId);
        // Group by userId and convert OrganizationPO objects to Organization entities
        Map<Long, List<Organization>> resultMap = new HashMap<>();
        for (User2OrgPO user2OrgPO : user2OrgPOList) {
            Long userId = user2OrgPO.getUserId();
            List<OrganizationPO> orgPOList = user2OrgPO.getOrganizationList();

            // Convert PO list to Entity list
            List<Organization> orgList = orgPOList.stream()
                    .map(OrganizationPO::toEntity)
                    .toList();

            resultMap.computeIfAbsent(userId, k -> new ArrayList<>()).addAll(orgList);
        }

        return resultMap;
    }

    /**
     * 根据组织ID列表获取组织实体列表
     *
     * @param orgIdList 组织ID列表，用于指定需要检索的组织
     * @return 返回一个组织实体列表，如果找不到任何组织则返回空列表
     */
    @Override
    public List<Organization> getOrganizationByIdList(List<Long> orgIdList) {
        List<OrganizationPO> organizationPOList = organizationMapper.selectByIdList(orgIdList);
        if (CollectionUtils.isEmpty(organizationPOList)) {
            return Collections.emptyList();
        }
        return organizationPOList.stream().map(OrganizationPO::toEntity).toList();
    }
}
