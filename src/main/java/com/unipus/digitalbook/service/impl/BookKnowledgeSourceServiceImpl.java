package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookKnowledgeSourceInfoMapper;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceCompleteInfoPO;
import com.unipus.digitalbook.service.BookKnowledgeSourceService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class BookKnowledgeSourceServiceImpl implements BookKnowledgeSourceService {

    @Resource
    private BookKnowledgeSourceInfoMapper bookKnowledgeSourceInfoMapper;

    /**
     * 根据主键删除记录
     *
     * @param id 记录的主键
     * @return 删除的记录数
     */
    public int deleteByPrimaryKey(Long id) {
        return bookKnowledgeSourceInfoMapper.deleteByPrimaryKey(id);
    }

    /**
     * 插入一条记录
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insert(BookKnowledgeResourceCompleteInfoPO record) {
        return bookKnowledgeSourceInfoMapper.insert(record);
    }

    /**
     * 插入一条记录（选择性字段）
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insertSelective(BookKnowledgeResourceCompleteInfoPO record) {
        return bookKnowledgeSourceInfoMapper.insertSelective(record);
    }

    /**
     * 根据主键查询记录
     *
     * @param id 记录的主键
     * @return 查询到的记录
     */
    public BookKnowledgeResourceCompleteInfoPO selectByPrimaryKey(Long id) {
        return bookKnowledgeSourceInfoMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据主键更新记录（选择性字段）
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKeySelective(BookKnowledgeResourceCompleteInfoPO record) {
        return bookKnowledgeSourceInfoMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据主键更新记录
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKey(BookKnowledgeResourceCompleteInfoPO record) {
        return bookKnowledgeSourceInfoMapper.updateByPrimaryKey(record);
    }
}
