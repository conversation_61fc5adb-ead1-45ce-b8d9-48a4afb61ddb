package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.SeriesPOMapper;
import com.unipus.digitalbook.model.entity.Series;
import com.unipus.digitalbook.model.po.SeriesPO;
import com.unipus.digitalbook.service.SeriesService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
public class SeriesServiceImpl implements SeriesService {

    @Resource
    private SeriesPOMapper seriesPOMapper;
    @Override
    public Series getSeriesById(Long seriesId) {
        SeriesPO seriesPO = seriesPOMapper.selectByPrimaryKey(seriesId);
        if (seriesPO == null) {
            return null;
        }
        return seriesPO.toEntity();
    }

    @Override
    public List<Series> batchGetSeriesByIds(Collection<Long> seriesIds) {
        if (CollectionUtils.isEmpty(seriesIds)) {
            return Collections.emptyList();
        }
        List<SeriesPO> seriesPOS = seriesPOMapper.selectByIds(seriesIds);
        return seriesPOS.stream().map(SeriesPO::toEntity).toList();
    }

    @Override
    public List<Series> getAllSeries() {
        List<SeriesPO> seriesPOS = seriesPOMapper.selectAll();
        if (CollectionUtils.isEmpty(seriesPOS)) {
            return Collections.emptyList();
        }
        return seriesPOS.stream().map(SeriesPO::toEntity).toList();
    }
}
