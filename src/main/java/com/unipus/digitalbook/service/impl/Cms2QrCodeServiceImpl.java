package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.qrcode.Cms2QrCodeException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.service.Cms2QrCodeService;
import com.unipus.digitalbook.service.UserService;
import com.unipus.digitalbook.service.remote.restful.qrcode.QrCodeApiService;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.AddQrCodeRequest;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeApiResponse;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeInfo;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.UpdateQrCodeRequest;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * cms2二维码服务接口实现类
 *
 * <AUTHOR>
 * @date 2025/2/17 17:41
 */
@Slf4j
@Service
public class Cms2QrCodeServiceImpl implements Cms2QrCodeService {

    @Resource
    private QrCodeApiService qrCodeApiService;
    @Resource
    private UserService userService;

    private static final String HEADER_CONTENT_DISPOSITION = "Content-Disposition";
    private static final String HEADER_ACCESS_CONTROL_EXPOSE_HEADERS = "Access-Control-Expose-Headers";

    /**
     * 根据用户ID获取cms2的token
     *
     * @param userId 用户ID
     * @return 生成的token字符串
     */
    @Override
    public String genToken(Long userId) {
        UserInfo userInfo = userService.getUserInfo(userId);
        String ssoId = userInfo.getSsoId();
        Map<String, Object> params = Map.of("openId", ssoId);
        QrCodeApiResponse<String> response = qrCodeApiService.genToken(params);
        log.info("调用cms2二维码生成token接口，返回结果：{}", JsonUtil.toJsonString(response));
        if (response == null || !response.isSuccess() || !StringUtils.hasText(response.getData())) {
            throw new Cms2QrCodeException("调用cms2二维码生成token接口失败");
        }
        return response.getData();
    }

    /**
     * 生成二维码列表
     *
     * @param bookId    教材ID
     * @param quantity  生成数量
     * @param pixelSize 二维码尺寸
     * @return 返回生成的二维码信息列表
     */
    @Override
    public List<QrCodeInfo> genQrCodes(String bookId, Integer quantity, Integer pixelSize) {
        AddQrCodeRequest addQrCodeRequest = new AddQrCodeRequest();
        addQrCodeRequest.setCourseId(bookId);
        addQrCodeRequest.setQrcodeNum(quantity);
        addQrCodeRequest.setQrcodeSize(pixelSize);
        addQrCodeRequest.setLinkType(1);
        QrCodeApiResponse<List<QrCodeInfo>> addResponse = qrCodeApiService.add(addQrCodeRequest);
        log.info("调用cms2添加二维码接口，返回结果：{}", JsonUtil.toJsonString(addResponse));
        if (addResponse == null || !addResponse.isSuccess() || CollectionUtils.isEmpty(addResponse.getData())) {
            throw new Cms2QrCodeException("调用cms2添加二维码接口失败");
        }
        return addResponse.getData();
    }

    /**
     * 更新二维码信息
     *
     * @param id           二维码ID
     * @param bookId       教材ID
     * @param bookInnerUrl 教材内部链接
     * @param remarks      备注
     * @return 更新后的二维码信息对象
     */
    @Override
    public QrCodeInfo updateQrCode(Long id, String bookId, String bookInnerUrl, String remarks) {
        UpdateQrCodeRequest updateQrCodeRequest = new UpdateQrCodeRequest();
        updateQrCodeRequest.setId(id);
        updateQrCodeRequest.setCourseId(bookId);
        updateQrCodeRequest.setRemark(remarks);
        updateQrCodeRequest.setResourceUrl(bookInnerUrl);
        updateQrCodeRequest.setPageNum(null);
        updateQrCodeRequest.setSupportVx(null);
        updateQrCodeRequest.setLinkType(null);
        QrCodeApiResponse<QrCodeInfo> updateResponse = qrCodeApiService.update(updateQrCodeRequest);
        log.info("调用cms2更新二维码，返回结果：{}", JsonUtil.toJsonString(updateResponse));
        if (updateResponse == null || !updateResponse.isSuccess() || updateResponse.getData() == null) {
            throw new Cms2QrCodeException("调用cms2更新二维码接口失败");
        }
        return updateResponse.getData();
    }


    /**
     * 查看二维码
     *
     * @param id       二维码ID
     * @param response HTTP响应对象
     */
    @Override
    public void displayQrCode(Long id, HttpServletResponse response) {
        ResponseEntity<org.springframework.core.io.Resource> displayResponse = qrCodeApiService.display(id);
        handleResource(displayResponse, "调用cms2查看二维码接口", response, null);
    }

    /**
     * 下载二维码
     *
     * @param id       二维码ID
     * @param response HTTP响应对象
     */
    @Override
    public void downloadQrCode(Long id, HttpServletResponse response) {
        ResponseEntity<org.springframework.core.io.Resource> displayResponse = qrCodeApiService.download(id);
        handleResource(displayResponse, "调用cms2下载二维码接口", response, null);
    }

    /**
     * 打包下载二维码
     *
     * @param ids      二维码ID列表
     * @param response HTTP响应对象
     */
    @Override
    public void downloadQrCodeZip(List<Long> ids, HttpServletResponse response) {
        Map<String, Object> params = Map.of("ids", ids);

        ResponseEntity<org.springframework.core.io.Resource> displayResponse = qrCodeApiService.downloadBach(params);
        String fileName = "QR" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".zip";
        handleResource(displayResponse, "调用cms2打包下载二维码接口", response, fileName);
    }

    /**
     * 处理Resource类型资源响应，将其内容写入到HttpServletResponse中
     *
     * @param responseEntity ResponseEntity对象，包含资源和HTTP响应状态
     * @param logPrefix      日志前缀，用于标识调用该方法的上下文
     * @param response       HttpServletResponse对象，用于向客户端发送响应
     * @throws Cms2QrCodeException 如果响应实体为空、状态码非200、或资源为空，则抛出运行时异常
     */
    private void handleResource(ResponseEntity<org.springframework.core.io.Resource> responseEntity, String logPrefix,
                                HttpServletResponse response, String fileName) {
        if (responseEntity == null || !HttpStatus.OK.equals(responseEntity.getStatusCode()) || responseEntity.getBody() == null) {
            throw new Cms2QrCodeException(logPrefix + "失败");
        }

        HttpHeaders headers = responseEntity.getHeaders();
        MediaType contentType = headers.getContentType();
        if (contentType != null && org.apache.commons.lang3.StringUtils.containsIgnoreCase(MediaType.APPLICATION_JSON_VALUE, contentType.toString())) {
            log.info("调用cms2下载二维码接口返回结果异常,返回结果：{}", JsonUtil.toJsonString(responseEntity.getBody()));
            throw new Cms2QrCodeException(logPrefix + "失败");
        }
        response.setCharacterEncoding("utf-8");
        response.setContentType(headers.getContentType().toString());
        if (StringUtils.hasText(fileName)) {
            response.setHeader(HEADER_CONTENT_DISPOSITION, "attachment;filename=" + fileName);
        } else {
            response.setHeader(HEADER_CONTENT_DISPOSITION, headers.getContentDisposition().toString());
        }
        response.setHeader(HEADER_ACCESS_CONTROL_EXPOSE_HEADERS, HEADER_CONTENT_DISPOSITION);

        org.springframework.core.io.Resource resource = responseEntity.getBody();
        if (resource == null) {
            throw new Cms2QrCodeException(logPrefix + "资源为空");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            StreamUtils.copy(resource.getInputStream(), outputStream);
        } catch (IOException e) {
            log.error("复制资源到响应输出流时发生IO异常", e);
            throw new Cms2QrCodeException("复制资源到响应输出流时发生IO异常");
        }
    }
}
