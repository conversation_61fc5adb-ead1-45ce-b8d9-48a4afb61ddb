package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.TenantPOMapper;
import com.unipus.digitalbook.model.enums.TenantExistFlagEnum;
import com.unipus.digitalbook.model.po.tenant.TenantPO;
import com.unipus.digitalbook.service.TenantService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 租户服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-04 17:21
 */
@Service
@Slf4j
public class TenantServiceImpl implements TenantService {

    @Resource
    private TenantPOMapper tenantPOMapper;

    /**
     * 检查租户是否存在
     *
     * <AUTHOR>
     * @date 2025-07-04 17:36
     * @param tenantId 租户 ID
     * @return 存在标识
     */
    @Override
    public TenantExistFlagEnum checkTenantExist(long tenantId) {
        if (tenantId <= 0L) {
            return TenantExistFlagEnum.NOT_EXIST;
        }

        // 查询租户信息
        TenantPO tenant = tenantPOMapper.selectById(tenantId);
        if (Objects.isNull(tenant)) {
            return TenantExistFlagEnum.NOT_EXIST;
        }

        return tenant.getEnable() ? TenantExistFlagEnum.EXIST : TenantExistFlagEnum.BANNED;
    }
}
