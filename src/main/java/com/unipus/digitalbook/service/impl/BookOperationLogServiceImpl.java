package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.BookOperationLogPOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.book.AddBookOperationLog;
import com.unipus.digitalbook.model.entity.book.SearchOperationLogList;
import com.unipus.digitalbook.model.enums.BookOperationEnum;
import com.unipus.digitalbook.model.po.book.BookOperationLogPO;
import com.unipus.digitalbook.model.po.book.SearchOperationLogPO;
import com.unipus.digitalbook.service.BookOperationLogService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 教材操作日志服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/16 14:23
 */
@Service
public class BookOperationLogServiceImpl implements BookOperationLogService {

    @Resource
    private BookOperationLogPOMapper bookOperationLogPOMapper;
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${kafka.topic.operationLog}")
    private String topicBookOperationLog;

    /**
     * 添加操作日志
     *
     * @param bookId           教材ID
     * @param operationUserId  操作用户ID
     * @param operationContent 操作内容
     * @param operationType    操作类型
     */
    @Override
    public void log(String bookId, Long operationUserId, String operationContent, Integer operationType) {
        if (!StringUtils.hasText(bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        if (operationUserId == null || operationUserId <= 0) {
            throw new IllegalArgumentException("操作用户ID不能为空");
        }
        if (operationContent == null
                || operationType == null || BookOperationEnum.getByCode(operationType) == null) {
            throw new IllegalArgumentException("操作内容或操作类型不能为空");
        }
        AddBookOperationLog addBookOperationLog = new AddBookOperationLog();
        addBookOperationLog.setBookId(bookId);
        addBookOperationLog.setOperationUserId(operationUserId);
        addBookOperationLog.setOperationTime(new Date());
        addBookOperationLog.setOperationContent(operationContent);
        addBookOperationLog.setOperationType(operationType);
        addBookOperationLog.setCreateTime(new Date());
        addBookOperationLog.setCreateBy(operationUserId);
        // 发送消息
        kafkaTemplate.send(topicBookOperationLog, JsonUtil.toJsonString(addBookOperationLog));
    }

    /**
     * 保存操作日志
     *
     * @param addBookOperationLog 描述添加图书操作的日志对象，包含操作的详细信息
     * @return 返回数据库生成的日志ID，用于唯一标识这条操作日志
     */
    @Override
    public Long saveOperationLogData(AddBookOperationLog addBookOperationLog) {
        BookOperationLogPO bookOperationLogPO = new BookOperationLogPO(addBookOperationLog);
        bookOperationLogPOMapper.insertSelective(bookOperationLogPO);
        return bookOperationLogPO.getId();
    }

    /**
     * 根据查询条件获取搜索操作日志列表
     *
     * @param bookId             教材ID
     * @param userName           操作用户名
     * @param operationStartTime 操作开始时间
     * @param operationEndTime   操作结束时间
     * @param pageParams         分页参数
     * @return 查询教材的操作日志列表，并返回操作日志的总数量
     */
    @Override
    public SearchOperationLogList searchOperationLogList(String bookId, String userName, Date operationStartTime, Date operationEndTime, PageParams pageParams) {
        if (!StringUtils.hasText(bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        List<SearchOperationLogPO> searchOperationLogPOList = bookOperationLogPOMapper.searchOperationLogList(bookId, userName, operationStartTime, operationEndTime, pageParams);
        Long count = bookOperationLogPOMapper.searchOperationLogListCount(bookId, userName, operationStartTime, operationEndTime);

        SearchOperationLogList bookOperationLogList = new SearchOperationLogList();
        bookOperationLogList.setOperationLogList(searchOperationLogPOList.stream().map(SearchOperationLogPO::toEntity).toList());
        bookOperationLogList.setTotal(count.intValue());
        return bookOperationLogList;
    }
}
