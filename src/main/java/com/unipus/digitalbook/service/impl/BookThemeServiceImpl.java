package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookThemePOMapper;
import com.unipus.digitalbook.dao.ThemePOMapper;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookTheme;
import com.unipus.digitalbook.model.entity.book.Theme;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.po.book.BookThemePO;
import com.unipus.digitalbook.model.po.book.ThemePO;
import com.unipus.digitalbook.service.BookService;
import com.unipus.digitalbook.service.BookThemeService;
import com.unipus.digitalbook.service.ResourcePermissionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 教材主题服务实现类
 */
@Service
public class BookThemeServiceImpl implements BookThemeService {

    @Resource
    private ThemePOMapper themePOMapper;

    @Resource
    private BookThemePOMapper bookthemePOMapper;

    @Resource
    private BookService bookService;

    @Resource
    private ResourcePermissionService resourcePermissionService;

    // 表示教材所有章节
    public static final String BOOK_ALL_CHAPTER_FLAG = "-";

    /**
     * 新增主题
     * @param theme 教材主题
     * @param userId 用户id
     * @return 新增主题ID
     */
    @Override
    public Long createTheme(Theme theme, Long userId) {
        ThemePO themePO = ThemePO.build(theme, userId);
        themePOMapper.insert(themePO);
        return themePO.getId();
    }

    /**
     * 编辑主题
     * @param theme 教材主题
     * @param userId 用户id
     * @return 保存结果
     */
    @Override
    public Boolean editTheme(Theme theme, Long userId) {
        themePOMapper.update(ThemePO.build(theme, userId));
        return true;
    }

    /**
     * 查询主题列表
     * @param theme 主题
     * @return 主题列表
     */
    @Override
    public List<Theme> getThemeList(Theme theme) {
        List<ThemePO> themePOs = themePOMapper.selectList(ThemePO.build(theme, null));
        if(CollectionUtils.isEmpty(themePOs)){
            return Collections.emptyList();
        }
        return themePOs.stream().map(ThemePO::toEntity).toList();
    }

    /**
     * 查询主题列表
     * @param ids 主题id列表
     * @return 主题列表
     */
    @Override
    public List<Theme> getThemeByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            throw new IllegalArgumentException("主题ID列表为空");
        }
        List<ThemePO> themePOs = themePOMapper.selectByIds(ids);
        if(CollectionUtils.isEmpty(themePOs)){
            return Collections.emptyList();
        }
        return themePOs.stream().map(ThemePO::toEntity).toList();
    }

    /**
     * 新增教材主题
     * @param bookTheme 教材主题实体
     * @param userId 用户id
     * @return 新增教材主题ID
     */
    @Override
    public Long createBookTheme(BookTheme bookTheme, Long userId) {
        // 判断用户是否拥有教材编辑权限
        if(Boolean.FALSE.equals(hasBookEditPermissions(bookTheme.getBookId(), bookTheme.getChapterId(), userId))){
            throw new IllegalArgumentException("用户没有编辑权限");
        }

        // 判断教材章节主题是否已存在
        List<BookThemePO> bookThemePOS = bookthemePOMapper.selectByBookIdAndChapterId(bookTheme.getBookId(), bookTheme.getChapterId());
        if(!CollectionUtils.isEmpty(bookThemePOS)){
            throw new IllegalArgumentException("教材章节主题已存在");
        }
        BookThemePO bookThemePO = BookThemePO.build(bookTheme, userId);
        bookthemePOMapper.insert(bookThemePO);
        return bookThemePO.getId();
    }

    /**
     * 编辑教材主题
     * @param bookTheme 教材主题实体
     * @param userId 用户id
     * @return 保存结果
     */
    @Override
    public Boolean editBookTheme(BookTheme bookTheme, Long userId) {
        // 判断教材章节主题是否已存在
        BookThemePO bookThemePO = bookthemePOMapper.selectById(bookTheme.getId());
        if(bookThemePO==null){
            throw new IllegalArgumentException("编辑的主题不存在");
        }
        // 判断用户是否拥有教材编辑权限
        if(Boolean.FALSE.equals(hasBookEditPermissions(bookThemePO.getBookId(), bookThemePO.getChapterId(), userId))){
            throw new IllegalArgumentException("用户没有编辑权限");
        }

        return bookthemePOMapper.update(BookThemePO.build(bookTheme, userId))>0;
    }

    /**
     * 检查用户是否有教材编辑权限
     * @param bookId 教材id
     * @param chapterId 章节id
     * @param userId 用户id
     * @return 是否有编辑权限
     */
    private Boolean hasBookEditPermissions(String bookId, String chapterId, Long userId){
        // 取得教材编者用户ID
        Book book = bookService.getBookById(bookId);
        if(book==null){
            throw new IllegalArgumentException("教材不存在");
        }
        // 教材创建者有编辑权限·
        if(book.getCreateBy().equals(userId)){
            return Boolean.TRUE;
        }
        // 协同用户不能更新全部章节主题
        if(BOOK_ALL_CHAPTER_FLAG.equals(chapterId)){
            throw new IllegalArgumentException("协同用户不能设置全部章节主题");
        }
        // 判断用户是否有编辑权限（系统用户对当前章节的权限）
        ResourcePermission resourcePermission = new ResourcePermission();
        resourcePermission.setResourceId(chapterId);
        resourcePermission.setResourceType(ResourceTypeEnum.CHAPTER.getCode());
        resourcePermission.setUserId(userId);
        resourcePermission.setPermissionType(PermissionTypeEnum.EDIT.getCode());
        return resourcePermissionService.hasResourcePermission(resourcePermission);
    }

    /**
     * 查询教材主题
     * @param bookTheme 教材主题实体
     * @return 主题列表
     */
    @Override
    public List<BookTheme> getBookThemes(BookTheme bookTheme){
        List<BookThemePO> bookThemePOS = bookthemePOMapper.selectByBookIdAndChapterId(bookTheme.getBookId(),
                bookTheme.getChapterId());
        if(CollectionUtils.isEmpty(bookThemePOS)){
            return Collections.emptyList();
        }
        return bookThemePOS.stream().map(BookThemePO::toEntity).toList();
    }

}