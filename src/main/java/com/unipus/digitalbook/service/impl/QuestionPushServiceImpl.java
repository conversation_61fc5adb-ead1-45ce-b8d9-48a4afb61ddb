package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.dao.QuestionThirdMappingPOMapper;
import com.unipus.digitalbook.grpc.QuesGrpcAddResponse;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.enums.ResultMessage;
import com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO;
import com.unipus.digitalbook.producer.TenantMessageProducer;
import com.unipus.digitalbook.service.QuestionPushService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class QuestionPushServiceImpl implements QuestionPushService {
    @Resource
    private QuestionThirdMappingPOMapper questionThirdMappingPOMapper;

    @Resource
    private TenantMessageProducer tenantMessageProducer;

    /**
     * 异步推送题目到第三方
     * @param question 题目
     * @param tenantId 租户ID
     */
    @Override
    public void pushQuestionToThirdAsync(BigQuestionGroup question, Long tenantId) {

        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                tenantId, MessageTopicEnum.ADD_QUESTION)).isPresent()) {
            CompletableFuture<QuesGrpcAddResponse> response = tenantMessageProducer.produceAsyncMessage(
                    tenantId,
                    MessageTopicEnum.ADD_QUESTION,
                    new TypeReference<>() {},
                    question,
                    new TypeReference<>() {});
            response.thenAccept((quesGrpcAddResponse) ->
                            processPushResponse(question, tenantId, quesGrpcAddResponse))
                    .exceptionally(throwable -> {
                        log.error("failed to async question message", throwable);
                        throw new BizException(ResultMessage.BIZ_REMOTE_ADD_QUESTION_FAIL, question.getId());
                    });
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.ADD_QUESTION.name(), tenantId);
        }
    }

    /**
     * 同步推送题目到第三方
     * @param question 题目
     * @param tenantId 租户ID
     */
    @Override
    public void pushQuestionToThirdSync(BigQuestionGroup question, Long tenantId) {
        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                tenantId, MessageTopicEnum.ADD_QUESTION)).isPresent()) {
            try{
                QuesGrpcAddResponse quesGrpcAddResponse = tenantMessageProducer.produceSyncMessage(
                        tenantId,
                        MessageTopicEnum.ADD_QUESTION,
                        new TypeReference<>() {
                        },
                        question,
                        new TypeReference<>() {
                        });
                processPushResponse(question, tenantId, quesGrpcAddResponse);
            }catch (Exception e){
                log.error("failed to async question message", e);
                throw new BizException(ResultMessage.BIZ_REMOTE_ADD_QUESTION_FAIL, question.getId());
            }
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.ADD_QUESTION.name(), tenantId);
        }
    }

    private void processPushResponse(BigQuestionGroup question, Long tenantId, QuesGrpcAddResponse quesGrpcAddResponse) {
        if (quesGrpcAddResponse == null) {
            log.error("failed to async question message {}", question.getId());
            throw new BizException(ResultMessage.BIZ_REMOTE_ADD_QUESTION_FAIL, question.getId());
        }

        long id = quesGrpcAddResponse.getId();
        int version = quesGrpcAddResponse.getVersion();
        QuestionThirdMappingPO questionThirdMappingPO = new QuestionThirdMappingPO(question);
        questionThirdMappingPO.setThirdId(String.valueOf(id));
        questionThirdMappingPO.setThirdVersion(String.valueOf(version));
        questionThirdMappingPO.setTenantId(tenantId);
        questionThirdMappingPOMapper.insertSelective(questionThirdMappingPO);
    }
}
