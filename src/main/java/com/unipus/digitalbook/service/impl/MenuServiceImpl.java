package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.dao.MenuPOMapper;
import com.unipus.digitalbook.model.entity.menu.Menu;
import com.unipus.digitalbook.model.entity.menu.ParentMenu;
import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.enums.RolePermissionTypeEnum;
import com.unipus.digitalbook.model.po.menu.MenuPO;
import com.unipus.digitalbook.service.MenuService;
import com.unipus.digitalbook.service.RoleService;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Sets;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MenuServiceImpl implements MenuService {
    @Resource
    private MenuPOMapper menuPOMapper;

    @Resource
    private RoleService roleService;

    /**
     * 超级管理员不展示的菜单路径
     */
    private static final Set<String> SUPER_ADMIN_MENU_NAME = Sets.newHashSet("系统管理");

    @Override
    public List<Menu> all(Long roleId) {
        List<MenuPO> menuPOS = menuPOMapper.selectAll();
        if (roleId == null || roleId <= 0) {
            return buildMenu(menuPOS, Collections.emptyList(), false);
        }
        Set<Long> assignedMenuIds = getAssignedMenuIds(Collections.singletonList(roleId));
        return buildMenu(menuPOS, assignedMenuIds, false);
    }

    @Override
    public List<Menu> list(Long userId, Long orgId) {
        if (userId == null || userId <= 0) {
            return Collections.emptyList();
        }
        List<MenuPO> menuPOS = menuPOMapper.selectAll();
        if (UserUtil.isSuperAdmin(userId)) {
            Set<Long> superAdminMenu = menuPOS.stream().filter(menuPO -> SUPER_ADMIN_MENU_NAME.contains(menuPO.getName()))
                    .map(MenuPO::getId).collect(Collectors.toSet());
            Set<Long> assignedMenuIds = new HashSet<>();
            menuPOS.forEach(menuPO -> {
                if (superAdminMenu.contains(menuPO.getParentId())) {
                    assignedMenuIds.add(menuPO.getId());
                }
                if (superAdminMenu.contains(menuPO.getId())) {
                    assignedMenuIds.add(menuPO.getId());
                    assignedMenuIds.add(menuPO.getParentId());
                }
            });
            return buildMenu(menuPOS, assignedMenuIds, true);
        }
        // 获取当前用户的角色
        List<Role> roles = roleService.userAssignedRoleList(userId, orgId);
        if (CollectionUtils.isEmpty(roles)) {
            return Collections.emptyList();
        }
        List<Long> roleIds = roles.stream().map(Role::getId).toList();
        Set<Long> assignedMenuIds = getAssignedMenuIds(roleIds);
        return buildMenu(menuPOS, assignedMenuIds, true);
    }

    private Set<Long> getAssignedMenuIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptySet();
        }
        Set<String> assignedPermissions = roleService.getAssignedPermissions(roleIds, RolePermissionTypeEnum.MENU);
        if (CollectionUtils.isEmpty(assignedPermissions)) {
            return Collections.emptySet();
        }
        return assignedPermissions.stream().map(Long::valueOf).collect(Collectors.toSet());
    }

    private List<Menu> buildMenu(List<MenuPO> menuPOList, Collection<Long> assignedMenuIds, boolean filter) {
        Map<Long, MenuPO> menuPOMap = menuPOList.stream().collect(Collectors.toMap(MenuPO::getId, menuPO -> menuPO));
        return menuPOList.stream().map(menuPO -> {
            Menu menu = menuPO.toMenu();
            MenuPO parentMenuPO = menuPOMap.get(menuPO.getParentId());
            if (parentMenuPO != null) {
                menu.setParentMenu(new ParentMenu(parentMenuPO.getId(), parentMenuPO.getName()));
            }
            menu.setAssigned(assignedMenuIds.contains(menuPO.getId()));
            if (filter && Boolean.FALSE.equals(menu.getAssigned())) {
                return null;
            }
            return menu;
        }).filter(Objects::nonNull).toList();
    }

    @Override
    public Long save(Menu menu) {
        if (menu == null) {
            throw new IllegalArgumentException("menu不能为空");
        }
        MenuPO menuPO = new MenuPO(menu);
        // 如果是新增
        if (menuPO.getId() == null || menuPO.getId() <= 0) {
            // 找到当前层级下最大的 位置
            menuPO.setPosition(menuPOMapper.findMaxPositionByParentId(menuPO.getParentId()) + 1);
            menuPOMapper.insertSelective(menuPO);
        } else {
            menuPOMapper.updateByPrimaryKeySelective(menuPO);
        }
        return menuPO.getId();
    }

    @Override
    public boolean delete(Long userId, Long menuId) {
        MenuPO menuPO = menuPOMapper.selectByPrimaryKey(menuId);
        if (menuPO == null || menuPO.getEnable() == Boolean.FALSE) {
            throw new IllegalArgumentException("菜单不存在");
        }
        List<MenuPO> menuPOS = menuPOMapper.selectSubByParentId(menuId);
        if (!menuPOS.isEmpty()) {
            throw new IllegalArgumentException("该菜单存在下级菜单，无法删除");
        }
        return menuPOMapper.logicalDelete(userId, menuId) > 0;
    }

    @Override
    public boolean changePosition(List<Long> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return false;
        }
        updatePosition(menuIds);
        return true;
    }

    private void updatePosition(List<Long> menuIds) {
        // 构建批量更新参数
        List<MenuPO> updateList = buildUpdateList(menuIds);
        // 执行批量更新
        if (!updateList.isEmpty()) {
            menuPOMapper.batchUpdatePosition(updateList);
        }
    }
    private List<MenuPO> buildUpdateList(List<Long> menuIds) {
        List<MenuPO> updateList = new ArrayList<>();
        for (int i = 0; i < menuIds.size(); i++) {
            // 构建更新对象
            MenuPO menuPO = new MenuPO();
            menuPO.setId(menuIds.get(i));
            // 设置新的排序值
            menuPO.setPosition(i + 1);
            updateList.add(menuPO);
        }
        return updateList;
    }
}
