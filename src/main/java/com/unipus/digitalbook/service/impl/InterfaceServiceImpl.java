package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.dto.ControllerPermissionListDTO;
import com.unipus.digitalbook.model.entity.AuthPermission;
import com.unipus.digitalbook.service.InterfaceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.PathPatternsRequestCondition;
import org.springframework.web.servlet.mvc.condition.RequestMethodsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 接口列表服务
 */
@Service
@Slf4j
public class InterfaceServiceImpl implements InterfaceService {

    @Resource
    private WebApplicationContext webApplicationContext;

    /**
     * 取得Controller的所有接口
     *
     * @return 权限列表
     */
    @Override
    public ControllerPermissionListDTO getAllPermissions() {
        RequestMappingHandlerMapping mapping = webApplicationContext.getBean("requestMappingHandlerMapping", RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = mapping.getHandlerMethods();

        List<AuthPermission> authPermissionList = handlerMethods.entrySet().stream()
                .filter(this::isControllerWithAnnotation)
                .map(this::convertToAuthResource)
                .toList();

        return new ControllerPermissionListDTO(authPermissionList.stream().collect(Collectors.groupingBy(AuthPermission::getController)));
    }

    private boolean isControllerWithAnnotation(Map.Entry<RequestMappingInfo, HandlerMethod> entry) {
        HandlerMethod method = entry.getValue();
        return (method.getBeanType().getAnnotation(RestController.class) != null ||
                method.getBeanType().getAnnotation(Controller.class) != null) &&
                method.getBeanType().getAnnotation(Tag.class) != null &&
                hasValidPath(entry.getKey());
    }

    private boolean hasValidPath(RequestMappingInfo mappingInfo) {
        PathPatternsRequestCondition patternsRequestCondition = mappingInfo.getPathPatternsCondition();
        return patternsRequestCondition != null && !patternsRequestCondition.getPatterns().isEmpty();
    }

    private String getRequestUrl(RequestMappingInfo mappingInfo) {
        PathPatternsRequestCondition patterns = mappingInfo.getPathPatternsCondition();
        return patterns != null ? patterns.getPatterns().iterator().next().getPatternString() : "";
    }

    private String getApiSummary(HandlerMethod method) {
        Operation annotation = method.getMethodAnnotation(Operation.class);
        return annotation != null ? annotation.summary() : "";
    }

    private String getApiDesc(HandlerMethod method) {
        Operation annotation = method.getMethodAnnotation(Operation.class);
        return annotation != null ? annotation.description() : "";
    }

    private String getRequestType(RequestMappingInfo mappingInfo) {
        RequestMethodsRequestCondition methodsCondition = mappingInfo.getMethodsCondition();
        return !methodsCondition.getMethods().isEmpty() ? methodsCondition.getMethods().iterator().next().name() : "";
    }

    private String getControllerDesc(HandlerMethod method) {
        Tag controllerTag = method.getBeanType().getAnnotation(Tag.class);
        return controllerTag != null ? controllerTag.name() : "";
    }

    private String getMethodName(HandlerMethod method) {
        return method != null ? method.getMethod().getName() : "";
    }

    private AuthPermission convertToAuthResource(Map.Entry<RequestMappingInfo, HandlerMethod> entry) {
        RequestMappingInfo mappingInfo = entry.getKey();
        HandlerMethod method = entry.getValue();

        String requestUrl = getRequestUrl(mappingInfo);
        String apiSummary = getApiSummary(method);
        String apiDesc = getApiDesc(method);
        String requestType = getRequestType(mappingInfo);
        String controllerName = method.getBean().toString();
        String controllerDesc = getControllerDesc(method);
        String methodName= getMethodName(method);
        log.debug("method:{}\n url:{}", JsonUtil.toJsonString(method.getMethod()), requestUrl);
        if (!apiSummary.isEmpty()) {
            log.debug("method_description:{}", apiSummary);
        }
        AuthPermission resource = new AuthPermission();
        resource.setController(controllerName);
        resource.setControllerDesc(controllerDesc);
        resource.setApiUrl(requestUrl);
        resource.setRequestType(requestType);
        resource.setMethodName(methodName);
        resource.setApiSummary(apiSummary);
        resource.setApiDesc(apiDesc);
        return resource;
    }

}
