package com.unipus.digitalbook.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unipus.digitalbook.common.exception.qrcode.QrCodeExportRelationalException;
import com.unipus.digitalbook.dao.QrCodePOMapper;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.qrcode.QrCode;
import com.unipus.digitalbook.model.entity.qrcode.QrCodeList;
import com.unipus.digitalbook.model.entity.qrcode.QrCodeRelationalTemplate;
import com.unipus.digitalbook.model.enums.QrCodeSizeEnum;
import com.unipus.digitalbook.model.params.qrcode.QrCodeVerificationParam;
import com.unipus.digitalbook.model.params.qrcode.SearchQrCodeParam;
import com.unipus.digitalbook.model.po.qrcode.QrCodePO;
import com.unipus.digitalbook.service.BookService;
import com.unipus.digitalbook.service.Cms2QrCodeService;
import com.unipus.digitalbook.service.QrCodeService;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeInfo;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QrCodeServiceImpl implements QrCodeService {

    @Resource
    private QrCodePOMapper qrCodePOMapper;
    @Resource
    private Cms2QrCodeService cms2QrCodeService;
    @Resource
    private BookService bookService;

    /**
     * 批量创建二维码
     *
     * @param bookName 教材名称
     * @param bookId   教材ID
     * @param quantity 生成数量
     * @param sizeEnum 二维码尺寸
     * @return 创建的二维码列表
     */
    @Override
    public Boolean createQrCodes(String bookName, String bookId, Integer quantity, QrCodeSizeEnum sizeEnum, Long userId) {
        List<QrCodeInfo> qrCodeInfoList = cms2QrCodeService.genQrCodes(bookId, quantity, sizeEnum.getPixels());
        List<QrCodePO> qrCodePOList = qrCodeInfoList.stream().map(qrCodeInfo -> {
            QrCodePO qrCodePO = new QrCodePO();
            qrCodePO.setId(qrCodeInfo.getId());
            qrCodePO.setQrCodeUrl(qrCodeInfo.getCodeShortUrl());
            qrCodePO.setQrCodeSize(sizeEnum.name());
            qrCodePO.setBookId(bookId);
            qrCodePO.setCreateBy(userId);
            qrCodePO.setCreateTime(new Date());
            qrCodePO.setEnable(true);
            return qrCodePO;
        }).toList();
        return qrCodePOMapper.batchInsert(qrCodePOList) > 0;
    }

    /**
     * 更新二维码信息
     *
     * @param qrCode 二维码信息
     */
    @Override
    public Boolean updateQrCode(QrCode qrCode) {
        QrCodeInfo qrCodeInfo = cms2QrCodeService.updateQrCode(qrCode.getId(), qrCode.getBookId(), qrCode.getBookInnerUrl(),
                qrCode.getRemarks());
        log.info("更新二维码信息，返回结果：{}", qrCodeInfo);
        QrCodePO qrCodePO = new QrCodePO(qrCode);
        return qrCodePOMapper.updateByPrimaryKeySelective(qrCodePO) > 0;
    }

    /**
     * 删除二维码
     *
     * @param id     二维码ID
     * @param userId 用户ID
     * @return 删除是否成功
     */
    @Override
    public Boolean deleteQrCode(Long id, Long userId) {
        QrCodePO qrCodePO = new QrCodePO();
        qrCodePO.setId(id);
        qrCodePO.setEnable(false);
        qrCodePO.setUpdateBy(userId);
        return qrCodePOMapper.updateByPrimaryKeySelective(qrCodePO) > 0;
    }

    /**
     * 根据ID获取二维码详情
     *
     * @param id 二维码ID
     * @return 二维码详情
     */
    @Override
    public QrCode getQrCodeById(Long id) {
        QrCodePO qrCodePO = qrCodePOMapper.selectByPrimaryKey(id);
        if (qrCodePO == null) {
            return null;
        }
        QrCode qrCode = qrCodePO.toEntity();
        Optional.ofNullable(bookService.getBookById(qrCode.getBookId()))
                .ifPresent(book -> qrCode.setBookName(book.getChineseName()));
        return qrCode;
    }

    /**
     * 分页查询二维码列表
     *
     * @param param 检索条件
     * @return 分页查询结果
     */
    @Override
    public QrCodeList searchQrCodes(SearchQrCodeParam param) {
        List<String> bookIdList = null;
        if (param.getBookName() != null && !param.getBookName().isEmpty()) {
            bookIdList = bookService.getByBookName(param.getBookName())
                    .stream().map(Book::getId).toList();
            if (bookIdList.isEmpty()) {
                return new QrCodeList(Collections.emptyList(), 0);
            }
        }
        Integer count = qrCodePOMapper.searchCount(bookIdList);
        if (count == null || count == 0) {
            return new QrCodeList(Collections.emptyList(), 0);
        }
        List<QrCode> qrCodeList = qrCodePOMapper.search(bookIdList, param.getPageParams())
                .stream().map(QrCodePO::toEntity).toList();
        // 批量获取书籍信息
        Map<String, String> bookMap = qrCodeList.stream()
                .map(QrCode::getBookId)
                .filter(bookId -> bookId != null && !bookId.isEmpty()).distinct()
                .map(bookService::getBookById).collect(Collectors.toMap(Book::getId, Book::getChineseName));
        qrCodeList.forEach(qrCode -> qrCode.setBookName(bookMap.get(qrCode.getBookId())));
        return new QrCodeList(qrCodeList, count);
    }

    /**
     * 验证二维码链接有效性
     *
     * @param results 二维码验证结果
     * @return 验证结果
     */
    @Override
    public Boolean verifyQrCodeLink(List<QrCodeVerificationParam.VerificationResult> results, Long userId) {
        // 验证二维码链接
        results.forEach(result -> {
            QrCodePO newQrCodePO = new QrCodePO();
            newQrCodePO.setId(result.getQrCodeId());
            newQrCodePO.setLinkVerificationStatus(result.getSuccess());
            newQrCodePO.setLinkVerificationTime(new Date());
            newQrCodePO.setUpdateBy(userId);
            qrCodePOMapper.updateByPrimaryKeySelective(newQrCodePO);
        });
        return true;
    }

    /**
     * 重新生成二维码图片
     *
     * @param id        二维码ID
     * @param pixelSize 二维码像素尺寸
     * @return 更新后的二维码信息
     */
    @Override
    public QrCode regenerateQrCode(Long id, Integer pixelSize) {
        return null;
    }

    /**
     * 保存二维码图片
     *
     * @param id         二维码ID
     * @param imageBytes 图片字节数组
     * @return 图片访问URL
     */
    @Override
    public String saveQrCodeImage(Long id, byte[] imageBytes) {
        return null;
    }

    /**
     * 导出二维码关系数据
     *
     * @param ids      二维码ID列表
     * @param userId   用户ID
     * @param response HTTP响应对象
     */
    @Override
    public void exportQrCodeRelational(List<Long> ids, Long userId, HttpServletResponse response) {
        // 获取二维码数据
        List<QrCodePO> qrCodePOList = qrCodePOMapper.selectByIds(ids);
        // 批量获取书籍信息
        Map<String, String> bookMap = qrCodePOList.stream()
                .map(QrCodePO::getBookId)
                .filter(bookId -> bookId != null && !bookId.isEmpty()).distinct()
                .map(bookService::getBookById).collect(Collectors.toMap(Book::getId, Book::getChineseName));
        // 转换为导出模板
        List<QrCodeRelationalTemplate> templateList = qrCodePOList.stream().map(qrCodePO -> {
            QrCodeRelationalTemplate template = new QrCodeRelationalTemplate();
            template.setId(qrCodePO.getId());
            template.setBookName(bookMap.get(qrCodePO.getBookId()));
            template.setRealBookLocation(qrCodePO.getRealBookLocation());
            template.setBookInnerUrl(qrCodePO.getBookInnerUrl());
            template.setQrCodeUrl(qrCodePO.getQrCodeUrl());
            template.setRemarks(qrCodePO.getRemarks());
            return template;
        }).toList();

        // 设置响应头
        String fileName = "QR" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        try (OutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, QrCodeRelationalTemplate.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("二维码关系数据")
                    .doWrite(templateList);
        } catch (IOException e) {
            log.error("导出二维码关系数据失败，用户ID: {}, 时间: {}", userId, LocalDateTime.now(), e);
            throw new QrCodeExportRelationalException();
        }
    }
}
