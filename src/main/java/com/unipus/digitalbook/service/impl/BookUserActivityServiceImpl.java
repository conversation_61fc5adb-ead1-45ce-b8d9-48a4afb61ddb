package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookUserActivityPOMapper;
import com.unipus.digitalbook.model.entity.book.BookUserActivity;
import com.unipus.digitalbook.model.po.book.BookUserActivityPO;
import com.unipus.digitalbook.service.BookUserActivityService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BookUserActivityServiceImpl implements BookUserActivityService {
    @Resource
    private BookUserActivityPOMapper bookUserActivityPOMapper;

    @Override
    public boolean batchSaveActivity(List<BookUserActivity> activityList) {
        if (activityList == null || activityList.isEmpty()) {
            return false;
        }
        List<BookUserActivityPO> bookUserActivityPOList = activityList.stream().map(BookUserActivityPO::new).toList();
        return bookUserActivityPOMapper.batchInsertOrUpdate(bookUserActivityPOList) > 0;
    }
}
