package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.PaperExtendPOMapper;
import com.unipus.digitalbook.dao.QuestionBankStrategyMapper;
import com.unipus.digitalbook.dao.QuestionGroupPOMapper;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.QuestionBank;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.po.paper.PaperPO;
import com.unipus.digitalbook.model.po.paper.QuestionBankPO;
import com.unipus.digitalbook.model.po.paper.QuestionBankStrategyPO;
import com.unipus.digitalbook.model.po.question.QuestionGroupPO;
import com.unipus.digitalbook.service.PaperOperationLogService;
import com.unipus.digitalbook.service.QuestionBankService;
import com.unipus.digitalbook.service.QuestionService;
import com.unipus.digitalbook.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 题库服务实现类
 */
@Service
@Slf4j
public class QuestionBankServiceImpl implements QuestionBankService {

    @Resource
    private QuestionService questionService;
    @Resource
    private UserService userService;
    @Resource
    private QuestionGroupPOMapper questionGroupPOMapper;
    @Resource
    private QuestionBankStrategyMapper questionBankStrategyMapper;
    @Resource
    private PaperOperationLogService paperOperationLogService;
    @Resource
    private PaperExtendPOMapper paperExtendPOMapper;

    /**
     * 保存题库
     *
     * @param questionBank 题库对象
     * @param userId       用户ID
     * @return 题库ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveQuestionBank(QuestionBank questionBank, Long userId) {
        String paperId = questionBank.getPaperId();
        // 检查新建题库名称是否重复
        checkQuestionBankName(questionBank);

        // 查询试卷主键ID
        Paper paper = getPaperWithDefaultVersion(paperId);
        Long paperPrimaryId = paper.getId();

        // 保存题库
        BigQuestionGroup bigQuestionGroup = questionBank.buildQuestionGroup(paperPrimaryId, userId);
        QuestionGroupPO questionGroupPO = new QuestionGroupPO(bigQuestionGroup);
        questionGroupPOMapper.insertOrUpdateSelective(questionGroupPO);

        // 查询题库策略
        QuestionBankStrategyPO oldStrategyPO = questionBankStrategyMapper
                .selectByBankIdAndVersion(questionBank.getBankId(), questionBank.getVersionNumber());
        QuestionBank oldQuestionBank = null;
        if(oldStrategyPO!=null) {
            questionBank.setId(oldStrategyPO.getId());
            oldQuestionBank = oldStrategyPO.toBankEntity();
        }

        // 保存题库策略
        questionBankStrategyMapper.insertOrUpdate(new QuestionBankStrategyPO(questionBank, userId));
        // 更新试题列表分数值
        updateQuestionScore(questionBank, oldQuestionBank);
        // 保存日志
        paperOperationLogService.addBankSaveOperation(paper, questionBank, oldQuestionBank, userId);

        // 返回题库ID
        return questionBank.getBankId();
    }

    /**
     * 检查题库名称
     * @param questionBank 题库对象
     * @exception IllegalArgumentException 题库名称重复
     */
    private void checkQuestionBankName(QuestionBank questionBank) {
        String bankName = questionBank.getBankName();
        String bankId = questionBank.getBankId();
        String paperId = questionBank.getPaperId();
        List<QuestionBank> questionBankList = getQuestionBankList(paperId);
        if(CollectionUtils.isEmpty(questionBankList)){
            return;
        }
        boolean exist = questionBankList.stream().anyMatch(qb ->
                !bankId.equals(qb.getBankId()) && bankName.equals(qb.getBankName()));
        if(exist){
            log.debug("题库名称重复:{}:{}", bankId, bankName);
            throw new IllegalArgumentException("题库名称重复");
        }
    }

    /**
     * 更新题库分数
     * @param questionBank 题库对象
     * @param oldQuestionBank 旧题库对象
     */
    private void updateQuestionScore(QuestionBank questionBank, QuestionBank oldQuestionBank) {
        // 分数未变更直接返回
        if (oldQuestionBank != null && Objects.equals(oldQuestionBank.getQuestionScore(), questionBank.getQuestionScore())) {
            log.debug("题库分数未变更，无需更新。{}", questionBank.getBankId());
            return;
        }
        // 取得题库题目列表
        String bankId = questionBank.getBankId();
        String versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        // 查询试卷或者题库下的题组列表
        List<QuestionGroupPO> questionGroupPOs = questionGroupPOMapper.selectChildByParentBizGroupId(bankId, versionNumber);
        if(CollectionUtils.isEmpty(questionGroupPOs)){
            log.debug("题库下没有题组信息:{}", bankId);
            return;
        }
        List<Long> groupIds = questionGroupPOs.stream().map(QuestionGroupPO::getId).toList();

        // 查询并更新题组分数
        List<BigQuestionGroup> questionGroups = questionService.batchGetBigQuestions(groupIds);
        if (CollectionUtils.isEmpty(questionGroups)) {
            return;
        }
        // 更新分数
        Integer questionScore = questionBank.getQuestionScore();
        for(BigQuestionGroup q : questionGroups){
            q.setScore(BigDecimal.valueOf(questionScore));
            updateScores(q.getQuestions(), questionScore);
        }
        // 保存更新后的题组
        questionService.batchSaveBigQuestions(questionGroups);
    }

    /**
     * 更新题目分数
     * @param questions 题目列表
     * @param totalScore 总分数
     */
    private void updateScores(List<Question> questions, float totalScore) {
        if(CollectionUtils.isEmpty(questions)) return;
        int count = questions.size();
        float baseScore = Math.round(totalScore/count*100)/100.0f;
        for(int i=0;i<count;i++){
            Question q = questions.get(i);
            float score = i==count-1 ? totalScore-baseScore*(count-1) : baseScore;
            q.setScore(BigDecimal.valueOf(score));
            updateScores(q.getQuestions(),score);
        }
    }

    /**
     * 获取题库列表
     *
     * @param paperId  试卷ID
     * @return 题库对象列表
     */
    @Override
    public List<QuestionBank> getQuestionBankList(String paperId) {
        String versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        // 通过试卷的业务ID查询题库列表
        Integer groupType = QuestionGroupTypeEnum.QUESTION_BANK.getCode();
        List<QuestionBankPO> questionBankPOs = questionGroupPOMapper.selectBankChildByPaperId(paperId, null, groupType, versionNumber);
        if(CollectionUtils.isEmpty(questionBankPOs)){
            log.debug("当前挑战卷没有配置题库:{}", paperId);
            return List.of();
        }

        // 查询用户名称
        List<Long> userIds = questionBankPOs.stream().map(QuestionBankPO::getCreateBy).toList();
        Map<Long, String> userMap = userService.getUserNames(userIds);

        // 转换为题库对象列表
        List<QuestionBank> questionBankList = new ArrayList<>();
        for(QuestionBankPO questionGroupPO : questionBankPOs){
            String userName = userMap.getOrDefault(questionGroupPO.getCreateBy(), "");
            // 转换为题库对象
            QuestionBank bank = questionGroupPO.toEntity(paperId, userName);
            questionBankList.add(bank);
        }
        return questionBankList;
    }

    /**
     * 获取题库详情
     * @param bankId 题库ID
     * @param versionNumber 版本号
     * @return 题库对象
     */
    @Override
    public QuestionBank getQuestionBankDetail(String bankId, String versionNumber) {
        String localVersionNumber = versionNumber==null ? IdentifierUtil.DEFAULT_VERSION_NUMBER : versionNumber;
        // 通过试卷的业务ID查询题库列表
        Integer groupType = QuestionGroupTypeEnum.QUESTION_BANK.getCode();
        List<QuestionBankPO> questionBankPOs = questionGroupPOMapper.selectBankChildByPaperId(null, bankId, groupType, localVersionNumber);
        if(CollectionUtils.isEmpty(questionBankPOs)){
            log.debug("没有题库信息:{}", bankId);
            return null;
        }
        return questionBankPOs.getFirst().toEntity(null);
    }

    /**
     * 删除题库
     *
     * @param bankId 题库业务ID
     * @param userId 当前用户ID
     * @return 题库ID
     */
    @Override
    public Boolean deleteQuestionBank(String bankId, Long userId) {
        // 查询试卷/题库
        QuestionBank questionBank = getQuestionBankWithDefaultVersion(bankId);

        // 删除试卷/题库
        Long groupId = questionService.deleteGroup(questionBank.getId(), userId);
        log.debug("删除题库成功:{}", groupId);

        // 输出操作日志
        paperOperationLogService.addBankDeleteOperation(questionBank, userId);
        return Boolean.TRUE;
    }

    /**
     * 根据题组ID从题组取得题组主键ID
     * @param paperId 试卷ID
     * @return 题组主键ID
     */
    private Paper getPaperWithDefaultVersion(String paperId) {
        String versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        PaperPO paperPO = paperExtendPOMapper.selectPaperDetail(paperId, versionNumber, false);
        if(paperPO==null){
            log.error("未能查询到试卷基本信息，paperId: {}, versionNumber: {}", paperId, versionNumber);
            throw new BizException("未能查询到试卷基本信息");
        }
        return paperPO.toEntity();
    }

    /**
     * 根据题组ID从题组取得题组主键ID
     * @param bizGroupId 题组业务ID(试卷ID/题库ID)
     * @return 题组主键ID
     */
    private QuestionBank getQuestionBankWithDefaultVersion(String bizGroupId) {
        String  versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        QuestionGroupPO questionGroupPO = questionGroupPOMapper.selectQuestionGroupByBizGroupId(bizGroupId, versionNumber);
        if(questionGroupPO == null){
            throw new IllegalArgumentException("题库不存在");
        }

        return new QuestionBank(questionGroupPO.toBigQuestion());
    }

    /**
     * 获取题库统计信息
     *
     * @param paperId  试卷ID
     * @return 题库对象列表
     */
    @Override
    public List<QuestionBank> getQuestionBankStatInfo(String paperId, String paperVersionNumber) {
        String versionNumber = paperVersionNumber== null ? IdentifierUtil.DEFAULT_VERSION_NUMBER : paperVersionNumber;
        // 通过试卷的业务ID查询题库列表
        Integer groupType = QuestionGroupTypeEnum.QUESTION_BANK.getCode();
        List<QuestionBankPO> questionBankPOs = questionGroupPOMapper.selectBankChildByPaperId(paperId, null, groupType, versionNumber);
        if(CollectionUtils.isEmpty(questionBankPOs)){
            return List.of();
        }
        return questionBankPOs.stream().map(po -> po.toEntity(paperId)).toList();
    }
}
