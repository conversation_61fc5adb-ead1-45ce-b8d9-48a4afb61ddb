package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.RedisUtil;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.service.PaperInstanceCacheManager;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PaperInstanceCacheManagerImpl implements PaperInstanceCacheManager {

    private static final String KEY_PREFIX_INSTANCE      = "PAPER_INSTANCE:%s";
    private static final String KEY_PREFIX_CONTENT       = "PAPER_INSTANCE:CONTENT:%s:%s";
    private static final long   INSTANCE_CACHE_SECONDS   = CacheConstant.REDIS_USER_PAPER_INSTANCE_TIMEOUT_HOURS * 3600L;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private QuestionService questionService;

    /**
     * 缓存试卷实例
     * @param paperInstance 试卷实例
     */
    @Override
    public void cachePaperInstance(PaperInstance paperInstance) {
        if(paperInstance==null){
            throw new IllegalArgumentException("试卷实例不能为空");
        }
        if(CollectionUtils.isEmpty(paperInstance.getBigQuestionGroupList())){
            throw new IllegalArgumentException("试卷实例关联的试题列表为空");
        }

        // 缓存试卷实例
        PaperInstanceCacher cacher = buildCacher(paperInstance);
        String instanceKey = buildInstanceKey(paperInstance.getInstanceId());
        redisUtil.writeToBucket(instanceKey, JsonUtil.toJsonString(cacher), INSTANCE_CACHE_SECONDS);

        // 缓存试卷内容
        String contentKey  = buildContentKey(paperInstance);
        redisUtil.writeToBucket(contentKey, paperInstance.getContent(), INSTANCE_CACHE_SECONDS);

        log.debug("缓存试卷实例成功: {}", paperInstance.getInstanceId());
    }

    /**
     * 获取试卷实例缓存
     * @param instanceId 实例ID
     * @return 试卷实例
     */
    @Override
    public PaperInstance getPaperInstanceCache(String instanceId) {
        if (!StringUtils.hasText(instanceId)) {
            return null;
        }

        // 读取试卷实例缓存
        String instanceKey = buildInstanceKey(instanceId);
        String cacherString = redisUtil.readFromBucket(instanceKey, String.class);
        if (!StringUtils.hasText(cacherString)) {
            log.debug("试卷实例缓存不存在: {}", instanceId);
            return null;
        }
        PaperInstanceCacher cacher = JsonUtil.parseObject(cacherString, PaperInstanceCacher.class);
        PaperInstance paperInstance = cacher.getSimplePaperInstance();

        // 读取试卷内容缓存
        String paperContent = redisUtil.readFromBucket(buildContentKey(paperInstance), String.class);
        paperInstance.setContent(paperContent);
        rebuildQuestions(paperInstance, cacher);

        log.debug("获取试卷实例缓存成功: {}", instanceId);
        return paperInstance;
    }

    // 重新构建题目
    private void rebuildQuestions(PaperInstance instance, PaperInstanceCacher cacher) {
        List<BigQuestionGroup> groups = questionService.batchGetBigQuestions(new ArrayList<>(cacher.getGroupIds()));
        if (CollectionUtils.isEmpty(groups)) {
            throw new IllegalArgumentException("未能查询到试卷题目组列表: " + instance.getInstanceId());
        }

        Set<Long> questionIds = cacher.getQuestionIds();
        groups.forEach(g -> g.setQuestions(filterQuestions(g.getQuestions(), questionIds)));
        instance.setBigQuestionGroupList(groups);
    }

    // 过滤题目
    private List<Question> filterQuestions(List<Question> questions, Set<Long> allowedIds) {
        if (CollectionUtils.isEmpty(questions)) {
            return List.of();
        }
        return questions.stream()
                .filter(q -> allowedIds.contains(q.getId()))
                .peek(q -> q.setQuestions(filterQuestions(q.getQuestions(), allowedIds)))
                .collect(Collectors.toList());
    }

    // 构建缓存器
    private PaperInstanceCacher buildCacher(PaperInstance paperInstance) {
        Set<Long> groupIds = paperInstance.getBigQuestionGroupList().stream().map(BigQuestionGroup::getId).collect(Collectors.toSet());

        PaperInstanceCacher cacher = new PaperInstanceCacher();
        cacher.setSimplePaperInstance(copyWithoutQuestions(paperInstance));
        cacher.setGroupIds(groupIds);
        cacher.setQuestionIds(collectQuestionIds(paperInstance));
        return cacher;
    }

    private Set<Long> collectQuestionIds(PaperInstance paperInstance) {
        Set<Long> ids = new HashSet<>();
        paperInstance.getBigQuestionGroupList().forEach(g -> traverseQuestions(g.getQuestions(), ids));
        return ids;
    }

    private void traverseQuestions(List<Question> questions, Set<Long> ids) {
        if (CollectionUtils.isEmpty(questions)){
            return;
        }
        questions.forEach(q -> {
            ids.add(q.getId());
            traverseQuestions(q.getQuestions(), ids);
        });
    }

    private PaperInstance copyWithoutQuestions(PaperInstance src) {
        PaperInstance dest = new PaperInstance();
        BeanUtils.copyProperties(src, dest, "content", "bigQuestionGroupList");
        return dest;
    }

    private String buildInstanceKey(String instanceId) {
        return KEY_PREFIX_INSTANCE.formatted(instanceId);
    }

    private String buildContentKey(PaperInstance pi) {
        return KEY_PREFIX_CONTENT.formatted(pi.getPaperId(), pi.getVersionNumber());
    }

    @Data
    private static class PaperInstanceCacher implements Serializable {
        private PaperInstance simplePaperInstance;
        private Set<Long> groupIds;
        private Set<Long> questionIds;
    }
}