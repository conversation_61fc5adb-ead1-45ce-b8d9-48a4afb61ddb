package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.DuplicateRecordException;
import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.exception.business.DeleteFailedException;
import com.unipus.digitalbook.common.exception.db.InsertFailedException;
import com.unipus.digitalbook.common.exception.user.UserAlreadyActivatedException;
import com.unipus.digitalbook.common.exception.user.UserAlreadyExistException;
import com.unipus.digitalbook.common.exception.user.UserNotExistException;
import com.unipus.digitalbook.dao.UserInfoPOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.model.entity.OrgInfo;
import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.entity.role.RoleUser;
import com.unipus.digitalbook.model.entity.user.SearchUser;
import com.unipus.digitalbook.model.entity.user.SearchUserList;
import com.unipus.digitalbook.model.enums.UserActivationStatus;
import com.unipus.digitalbook.model.po.UserInfoPO;
import com.unipus.digitalbook.model.po.role.RoleUserRelationPO;
import com.unipus.digitalbook.model.po.user.SearchUserAndOrgPO;
import com.unipus.digitalbook.service.OrgService;
import com.unipus.digitalbook.service.RoleService;
import com.unipus.digitalbook.service.UserService;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    UserInfoPOMapper userInfoPOMapper;
    @Resource
    OrgService orgService;
    @Resource
    RoleService roleService;

    /**
     * 根据手机号和机构ID和状态查询用户列表
     *
     * @param phone  手机号
     * @param orgId  机构ID
     * @param status 状态
     * @return 用户列表
     */
    @Override
    public List<UserInfo> searchByPhoneAndOrgAndStatus(String phone, Long orgId, Integer status, PageParams pageParams) {
       List<UserInfoPO> userInfoPOList = userInfoPOMapper.searchByPhoneAndOrgAndStatus(phone, status, orgId,pageParams);
       if (userInfoPOList==null||userInfoPOList.isEmpty()){
           return new ArrayList<>(0);
       }
       return userInfoPOList.stream().map(UserInfoPO::toEntity).toList();
    }

    /**
     * 根据机构ID和关键词查询有效的用户列表
     * @param orgId 机构ID（可以为空）
     * @param keyword 关键词（匹配用户名称或者手机号，可以为空）
     * @param page 分页参数(可以为空)
     * @return 有效的用户列表
     */
    @Override
    public List<UserInfo> selectValidUsersByOrgAndKeyword(Long orgId, String keyword, PageParams page){
        List<UserInfoPO> userInfoPOList = userInfoPOMapper.selectValidUsersByOrgAndKeyword(orgId, keyword, page);
        if (CollectionUtils.isEmpty(userInfoPOList)){
            return List.of();
        }
        return userInfoPOList.stream().map(UserInfoPO::toEntity).toList();
    }

    /**
     * 根据机构ID和用户ID列表查询用户的有效性
     * @param orgId 机构ID（可以为空）
     * @param userIds 用户ID列表（不为空）
     * @return 有效的用户ID列表
     */
    @Override
    public Set<Long> checkUserValidityWithUserIdsAndOrgId(Long orgId, List<Long> userIds){
        if(CollectionUtils.isEmpty(userIds)){
            throw new IllegalArgumentException("userIds不能为空");
        }
        return userInfoPOMapper.checkUserValidityWithUserIdsAndOrgId(orgId, userIds);
    }

    /**
     * 根据SSO ID获取用户信息
     *
     * @param ssoId SSO ID
     * @return 用户信息对象
     */
    @Nullable
    @Override
    public UserInfo getUserInfoBySsoId(String ssoId) {
        if (ssoId == null) {
            return null;
        }
        UserInfoPO userInfoPO = userInfoPOMapper.selectBySsoId(ssoId);
        if (userInfoPO == null) {
            return null;
        }
        // 创建一个新的UserInfo对象，并将从数据库中获取的信息填充到该对象中
        return userInfoPO.toEntity();
    }

    /**
     * 根据手机号获取用户信息
     *
     * @param cellPhone 手机号
     * @return 用户信息对象，如果未找到则返回null
     */
    @Override
    public UserInfo getUserInfoByCellPhone(String cellPhone) {
        // 检查手机号是否为空或空白字符
        if (StringUtils.hasText(cellPhone)) {
            // 根据手机号查询数据库中的用户信息
            UserInfoPO userInfoPO = userInfoPOMapper.selectByCellPhone(cellPhone);
            // 如果未找到用户信息，则返回null
            if (userInfoPO == null) {
                return null;
            }
            return userInfoPO.toEntity();
        }

        // 如果手机号为空或空白字符，则返回null
        return null;
    }

    /**
     * 获取登录用户信息
     *
     * @param ssoId SSO ID
     * @param mobile 手机号
     * @return 用户信息对象
     */
    @Override
    public Response<CurrentUserInfo> getLoginUserInfo(String ssoId, String mobile) {
        CurrentUserInfo currentUserInfo = new CurrentUserInfo();

        // 根据SSO ID获取用户信息
        UserInfo userInfo = getUserInfoBySsoId(ssoId);
        if (userInfo==null){
            // 还未激活
            userInfo= getUserInfoByCellPhone(mobile);
            if (userInfo==null){
                return Response.fail("未查到当前用户在ipublish的身份信息。");
            }
            if (StringUtils.hasText(userInfo.getSsoId())){
                return Response.fail("当前手机号对应的身份已经绑定了其他sso账号。");
            }
            currentUserInfo.setActive(Boolean.FALSE);
        }else{
            // 已激活
            currentUserInfo.setActive(Boolean.TRUE);
        }

        // 设置用户信息
        currentUserInfo.setUserInfo(userInfo);
        // 设置用户登录时间
        currentUserInfo.setLastLoginTime(Date.from(Instant.now()));
        // 取得用户登录ID
        Long userId = userInfo.getId();
        // 取得用户所属组织列表
        List<Organization> organizations = orgService.getUserOrgs(userId);
        currentUserInfo.setOrgInfoList(organizations.stream().map(OrgInfo::build).toList());
        // 设置用户组织角色信息映射
        Set<Long> orgIds = organizations.stream().map(Organization::getId).collect(Collectors.toSet());
        currentUserInfo.setOrgRolesMap(getUserOrgRolesMap(userId, orgIds));

        return Response.success(currentUserInfo);
    }

    /**
     * 取得用户组织角色信息映射
     * @param userId 用户ID
     * @return 用户组织角色信息映射
     */
    public Map<Long, Set<Long>> getUserOrgRolesMap(Long userId, Set<Long> orgIds) {
        if(CollectionUtils.isEmpty(orgIds)){
            return Collections.emptyMap();
        }

        // 获取用户所属组织的角色信息
        List<RoleUserRelationPO> userRoles = roleService.getByUserId(userId);
        if(CollectionUtils.isEmpty(userRoles)){
            String message = String.format("用户[%s]未设定角色", userId);
            log.warn(message);
            return Collections.emptyMap();
        }

        // 按照组织ID分组
        return userRoles.stream()
                .filter(r->orgIds.contains(r.getOrgId()))
                .collect(Collectors.groupingBy(RoleUserRelationPO::getOrgId,
                        Collectors.mapping(RoleUserRelationPO::getRoleId, Collectors.toSet())));
    }

    /**
     * 根据条件搜索用户列表
     *
     * @param orgId      组织ID，用于限定搜索的组织范围
     * @param cellPhone  手机号，用于搜索特定的用户
     * @param userName   用户名，用于搜索特定的用户
     * @param status     用户状态，用于过滤用户
     * @param pageParams 分页参数，用于指定查询的页码和每页数量
     * @return SearchUserList 包含用户列表和总数量的对象
     */
    @Override
    public SearchUserList searchUserList(Long orgId, String cellPhone, Integer status, String userName, PageParams pageParams) {
        List<SearchUserAndOrgPO> userInfoList = userInfoPOMapper.searchUserAndOrg(orgId, cellPhone, status, userName, pageParams);
        Long count = userInfoPOMapper.searchUserAndOrgCount(orgId, cellPhone, status, userName);
        Map<Long, String> orgMap;
        if (!CollectionUtils.isEmpty(userInfoList)) {
            List<Long> orgIdList = userInfoList.stream().map(SearchUserAndOrgPO::getOrgId).distinct().toList();
            List<Organization> organizationList = orgService.getOrganizationByIdList(orgIdList);
            orgMap = organizationList.stream().collect(Collectors.toMap(Organization::getId, Organization::getOrgName));
        } else {
            orgMap = new HashMap<>();
        }
        List<SearchUser> userList = userInfoList.stream().map(userInfo -> {
            List<Role> roleList = roleService.userAssignedRoleList(userInfo.getId(), userInfo.getOrgId());
            List<String> roleNameList = roleList.stream().map(Role::getName).toList();
            SearchUser searchUser = userInfo.toEntity();
            searchUser.setOrg(orgMap.get(userInfo.getOrgId()));
            searchUser.setRoleList(roleNameList);
            return searchUser;
        }).toList();
        SearchUserList searchUserList = new SearchUserList();
        searchUserList.setUserList(userList);
        searchUserList.setTotal(count.intValue());
        return searchUserList;
    }

    /**
     * 激活用户
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @Override
    public Boolean activateUser(Long userId,String ssoId) {
        // 不一定谁还会调用这个功能 所以还是查一下保险
        UserInfoPO userInfoPO = userInfoPOMapper.selectByPrimaryKey(userId);
        // 判断用户是否存在
        if (userInfoPO == null) {
            return false; // 用户不存在，返回false
        }
        int rows = userInfoPOMapper.activateUser(userId, ssoId);
        return rows > 0;
    }

    /**
     * 添加用户,并添加到指定组织，并分配角色
     *
     * @param userInfo 用户信息对象，包含用户的详细信息
     * @param orgId    组织ID，用户将被添加到该组织
     * @param roleIds  角色ID列表，用户将被赋予这些角色
     * @param opUserId 操作者ID，执行此操作的用户ID
     * @return 操作是否成功
     */
    @Override
//    @Transactional(rollbackFor = {InsertFailedException.class,IllegalArgumentException.class})
    public Boolean addUser(UserInfo userInfo, Long orgId, List<Long> roleIds, Long opUserId) {
        //验证机构存在
        Boolean orgExist = orgService.existById(orgId);
        if (Boolean.FALSE.equals(orgExist)) {
            throw new IllegalArgumentException("机构不存在");
        }
        // 检查用户是否存在
        UserInfoPO userInfoPO = userInfoPOMapper.selectByCellPhone(userInfo.getCellPhone());
        if (userInfoPO == null) {
            // 用户不存在，创建新用户
            userInfoPO = new UserInfoPO().createNew(userInfo.getName(), userInfo.getCellPhone());
            userInfoPO.setCreateBy(opUserId);
            if (userInfoPOMapper.insertSelective(userInfoPO) <= 0) {
                throw new InsertFailedException("新建用户失败");
            }
        } else if (Boolean.TRUE.equals(orgService.userExistInOrg(userInfoPO.getId(), orgId))) { // 使用 else if 简化逻辑
            throw new DuplicateRecordException("该用户在机构中已存在");
        }
        Long userId = userInfoPO.getId(); // 提取 userId 变量
        // 加入机构
        if (Boolean.FALSE.equals(orgService.joinOrganization(userId, orgId, opUserId))) {
            throw new InsertFailedException("加入机构失败");
        }


        RoleUser roleUser = new RoleUser();
        roleUser.setUserId(userInfoPO.getId());
        roleUser.setRoleIds(roleIds);
        roleUser.setOrgId(orgId);
        boolean assignRoleSuccess = roleService.bathAssignRole2User(roleUser, opUserId);
        if (!assignRoleSuccess) {
            throw new InsertFailedException("分配角色失败");
        }
        return true;
    }

    /**
     * 编辑用户信息（管理员操作）
     *
     * @param userInfo 用户信息对象，包含需要更新的用户数据
     * @param opUserId 操作用户的ID，用于记录谁进行了此次更新
     * @return 返回布尔值，指示操作是否成功
     */
    @Override
    public Boolean editUser4Admin(UserInfo userInfo, Long opUserId) {
        UserInfoPO userInfoPO = userInfoPOMapper.selectByPrimaryKey(userInfo.getId());
        if (userInfoPO==null){
            throw new UserNotExistException();
        }

        if (UserActivationStatus.ACTIVE.getCode()==userInfoPO.getStatus()){
            throw new UserAlreadyActivatedException("用户已激活，不可修改。");
        }
        UserInfoPO exist = userInfoPOMapper.selectByCellPhone(userInfo.getCellPhone());
        if (exist!=null&&!exist.getId().equals(userInfo.getId())){
            throw new UserAlreadyExistException("注册手机号已是被其他用户使用。");
        }

        userInfoPO.setName(userInfo.getName());
        userInfoPO.setCellPhone(userInfo.getCellPhone());
        int updateRow =  userInfoPOMapper.updateByPrimaryKeySelective(userInfoPO);
        return updateRow > 0;
    }

    /**
     * 获取用户信息（用户基本信息）
     *
     * @param userId 用户ID
     * @return 用户信息对象
     */
    @Override
    public UserInfo getUserInfo(Long userId) {
        // 取得用户信息
        UserInfoPO userInfoPO = userInfoPOMapper.selectByPrimaryKey(userId);
        if(userInfoPO==null){
            throw new UserNotExistException();
        }
        // 返回用户信息
        return userInfoPO.toEntity();
    }

    /**
     * 获取用户信息（包含组织角色映射）
     *
     * @param userId 用户ID
     * @param orgId  组织ID
     * @return 用户信息对象
     */
    @Override
    public CurrentUserInfo getCurrentUserInfo(Long userId, Long orgId) {
        // 取得用户信息
        UserInfoPO userInfoPO = userInfoPOMapper.selectByPrimaryKey(userId);
        if(userInfoPO==null){
            throw new UserNotExistException();
        }

        // 取得组织信息
        Organization organization = orgService.getOrgDetail(orgId);
        List<OrgInfo> orgInfos =  organization!=null ? List.of(OrgInfo.build(organization)) : new ArrayList<>();
        Set<Long> orgIds = orgInfos.stream().map(OrgInfo::getId).collect(Collectors.toSet());

        // 取得用户组织/角色映射信息
        Map<Long, Set<Long>> userOrgRolesMap = getUserOrgRolesMap(userId, orgIds);
        if(CollectionUtils.isEmpty(userOrgRolesMap)){
            log.info("用户组织/角色映射信息不存在");
        }

        // 返回用户信息
        return CurrentUserInfo.build(userInfoPO.toEntity(), orgInfos, userOrgRolesMap, null, userInfoPO.getActiveTime()!=null);
    }

    /**
     * 更新用户名称
     *
     * @param userInfo 用户信息对象
     * @param opUserId 操作者ID
     * @return 更新是否成功
     */
    @Override
    public Boolean updateCurrentUserInfo(UserInfo userInfo, Long opUserId) {

        UserInfoPO userInfoPO = new UserInfoPO();
        userInfoPO.setId(opUserId);
        userInfoPO.setName(userInfo.getName());
        userInfoPO.setUpdateBy(opUserId);
        int count = userInfoPOMapper.updateByPrimaryKeySelective(userInfoPO);
        return count > 0;
    }

    @Override
    public Map<Long, UserInfo> getUserMap(Collection<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<UserInfoPO> userInfos = userInfoPOMapper.bathSelectUser(userIds);
        if (CollectionUtils.isEmpty(userInfos)) {
            return Collections.emptyMap();
        }
        return userInfos.stream().map(UserInfoPO::toEntity)
                .collect(Collectors.toMap(UserInfo::getId, userInfo -> userInfo));
    }

    /**
     * 根据用户ID查询用户名称
     * @param userIds 用户ID集合
     * @return 用户名称集合
     */
    @Override
    public Map<Long, String> getUserNames(List<Long> userIds) {
        Map<Long, UserInfo> userMap = getUserMap(userIds);
        if(CollectionUtils.isEmpty(userMap)){
            throw new BizException("用户不存在");
        }
        return userMap.entrySet().stream()
                .collect(Collectors.toMap( Map.Entry::getKey, entry -> entry.getValue().getName()));
    }

    /**
     * 根据用户id列表 批量删除用户
     *
     * @param userIdList 用户ID列表
     * @param opUserId   操作者ID
     * @return 删除是否成功
     */
    @Override
    public int deleteUsers(List<Long> userIdList, Long opUserId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return 0;
        }
        if (opUserId==null||opUserId<=0){
            throw new IllegalArgumentException("操作者ID不能为空");
        }
        int rows= userInfoPOMapper.batchDelete(userIdList, opUserId);
        if (rows<=0){
            throw new DeleteFailedException("deleteUsers删除失败"+userIdList+"-"+opUserId);
        }
        return rows;
    }
}
