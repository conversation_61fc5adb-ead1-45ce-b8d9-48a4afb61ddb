package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.dao.BookPOMapper;
import com.unipus.digitalbook.dao.ChapterPOMapper;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.enums.ResultMessage;
import com.unipus.digitalbook.model.params.book.BookCollaboratorParam;
import com.unipus.digitalbook.model.params.book.CollaboratorUserParam;
import com.unipus.digitalbook.model.params.book.PermissionSearchParam;
import com.unipus.digitalbook.model.po.book.BookPO;
import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import com.unipus.digitalbook.service.BookPermissionService;
import com.unipus.digitalbook.service.ResourcePermissionService;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 教材权限实现类
 */
@Service
@Slf4j
public class BookPermissionServiceImpl implements BookPermissionService {

    @Resource
    private ResourcePermissionService resourcePermissionService;

    @Resource
    private ChapterPOMapper chapterPOMapper;

    @Resource
    private BookPOMapper bookPOMapper;

    /**
     * 追加教材作者权限
     *
     * @param bookId    教材ID
     * @param creatorId 创建者ID
     * @return 是否追加成功
     */
    @Override
    public Boolean addBookOwnerPermission(String bookId, Long creatorId) {
        List<ResourcePermission> resourcePermissions = new ArrayList<>();
        // 设置教材作者权限
        ResourcePermission permissionParam = new ResourcePermission();
        // 设置教材ID
        permissionParam.setResourceId(bookId);
        // 设置教材类型为书籍
        permissionParam.setResourceType(ResourceTypeEnum.BOOK.getCode());
        // 设置教材作者ID
        permissionParam.setUserId(creatorId);
        // 设置权限类型为教材作者
        permissionParam.setPermissionType(PermissionTypeEnum.OWNER.getCode());
        resourcePermissions.add(permissionParam);

        // 更新教材作者权限
        return resourcePermissionService.updateUserPermission(resourcePermissions, creatorId);
    }

    /**
     * 查询当前组织非协作者用户
     *
     * @param param  查询参数
     * @param orgId  组织ID
     * @param userId 用户ID
     * @return 操作是否成功
     */
    @Override
    public List<ResourceUser> getNonCollaboratorUser(CollaboratorUserParam param, Long orgId, Long userId) {
        return resourcePermissionService.getNoPermissionUser(
                orgId, param.getChapterId(), PermissionTypeEnum.EDIT.getCode(), param.getKeyword(), null);
    }

    /**
     * 查询教材的协作者
     *
     * @param param  查询参数
     * @param orgId  组织ID
     * @param userId 用户ID
     * @return 操作是否成功
     */
    @Override
    public List<ResourceUser> getBookCollaborator(CollaboratorUserParam param, Long orgId, Long userId) {
        // 查询协作者的教材阅读权限（该协作者拥有某个章节的编辑权限），即可取得该教材的协作者列表
        return resourcePermissionService.getResourceUsers(orgId, param.getChapterId(),
                ResourceTypeEnum.CHAPTER, PermissionTypeEnum.EDIT, param.getKeyword());
    }

    /**
     * 设置教材的协作者
     * 1.检查用户是否教材的作者（只有教材拥有者才能添加该教材的协作者）
     * 2.更新存在的资源权限(保留新增权限，删除最新用户列表以外的权限)
     * 3.设置协作者的教材权限
     *  1）设置教材阅读权限和章节编辑权限(补充新增权限，增加历史数据中不存在的权限，忽略历史数据中已存在的权限)
     *  2）设置章节的协作者编辑权限（该章节的协作者拥有该章节的编辑权限）
     * 4.设置教材的协作者阅读权限（该教材的协作者拥有该教材的阅读权限）
     * @param param 教材协作者参数
     * @param ownerId 当前用户ID
     * @return 操作是否成功
     */
    @Override
    @Transactional
    //@DataPermission(resourceId = "#param.bookId", permissionType = PermissionTypeEnum.OWNER)
    public Boolean updateBookCollaborator(BookCollaboratorParam param, Long ownerId) {
        // 1.检查用户是否教材的作者（只有教材拥有者才能添加该教材的协作者）
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(param.getBookId());
        if (bookPO == null || !ownerId.equals(bookPO.getEditorId())) {
            log.error("教材不存在，或者教材作者不是当前用户, 教材ID:{}, 章节ID:{}", param.getBookId(), param.getChapterId());
            throw new BizException(ResultMessage.BIZ_RESOURCE_PERMISSION_ADD_FAIL, "教材");
        }
        List<Long> userIds = param.getUserIds()==null ? new ArrayList<>(): param.getUserIds();

        // 2.更新存在的资源权限(保留新增权限，删除最新用户列表以外的权限)
        List<ResourcePermission> permissions = getNeedUpdatePermissions(param.getBookId(), param.getChapterId(), userIds);

        // 3.设置教材阅读权限和章节编辑权限(补充新增权限，增加历史数据中不存在的权限，忽略历史数据中已存在的权限)
        for (Long userId : userIds) {
            // 设置教材协作者阅读权限
            mergePermissionList(permissions, param.getChapterId(), ResourceTypeEnum.CHAPTER, PermissionTypeEnum.EDIT, userId);
            // 设置章节协作者编辑权限
            mergePermissionList(permissions, param.getBookId(), ResourceTypeEnum.BOOK, PermissionTypeEnum.READ, userId);
        }
        if(CollectionUtils.isEmpty(permissions)){
            log.info("教材编辑权限更新对象为空(保持当前状态，无需更新)");
            return true;
        }

        // 4.更新教材协作者权限（资源ID相同的权限，按照顺序更新，保证删除与新增权限不冲突）
        Boolean result = resourcePermissionService.updateUserPermission(permissions, ownerId);
        if(Boolean.FALSE.equals(result)){
            log.error("更新教材协作者权限失败, 教材ID:{}, 章节ID:{}", param.getBookId(), param.getChapterId());
            throw new BizException(ResultMessage.BIZ_RESOURCE_PERMISSION_ADD_FAIL, "教材");
        }
        log.debug("更新教材协作者权限成功, 教材ID:{}, 章节ID:{}", param.getBookId(), param.getChapterId());
        return true;
    }

    /**
     * 检查用户是否有编辑权限
     * @param userId 用户ID
     * @param bookId 教材ID
     * @param chapterId 章节ID
     * @return 是否有编辑权限 （true：有编辑权限，false：没有编辑权限）
     */
    @Override
    @NotNull
    public Boolean hasEditePermission(Long userId, String bookId, String chapterId) {
        if (userId == null || StringUtils.isBlank(bookId) || StringUtils.isBlank(chapterId)) {
            return false;
        }
        if (Boolean.TRUE.equals(resourcePermissionService.hasResourcePermission(
                new ResourcePermission(bookId, ResourceTypeEnum.BOOK.getCode(), userId, PermissionTypeEnum.OWNER.getCode())))) {
            return true;
        }
        if (Boolean.TRUE.equals(resourcePermissionService.hasResourcePermission(
                new ResourcePermission(chapterId, ResourceTypeEnum.CHAPTER.getCode(), userId, PermissionTypeEnum.EDIT.getCode())))) {
            return true;
        }
        log.warn("用户没有编辑权限, 用户ID:{}, 教材ID:{}, 章节ID:{}", userId, bookId, chapterId);
        return false;
    }

    /**
     * 合并教材的协作者权限合并可能被删除后有追加的权限）
     * 1.如果列表中存在同样的权限(表示数据库存在相同权限)，则不需要变更，需要从更新对象列表中删除
     * 2.如果列表中不存在同样的权限，则新增权限
     * @param permissions 权限列表
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param permissionType 权限类型
     * @param userId 用户ID
     */
    private void mergePermissionList(List<ResourcePermission> permissions, String resourceId,
                                 ResourceTypeEnum resourceType, PermissionTypeEnum permissionType, Long userId) {

        Optional<ResourcePermission> target = Optional.of(permissions).orElse(new ArrayList<>()).stream()
                .filter(p -> p.getResourceId().equals(resourceId) && p.getUserId().equals(userId))
                .findFirst();
        if(target.isPresent() && target.get().getPermissionType().equals(permissionType.getCode())){
            // 如果列表中存在同样的权限(表示数据库存在相同权限)，则不需要变更，需要从更新对象列表中删除
            permissions.remove(target.get());
        }else{
            // 如果列表中不存在同样的权限，则新增权限
            permissions.add(buildPermissionParam(resourceId, resourceType, permissionType.getCode(), userId));
        }
    }

    /**
     * 设置权限参数对象
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @param permissionTypeCode 权限类型
     * @param userId 用户ID
     * @return 权限参数对象
     */
    private ResourcePermission buildPermissionParam(
            String resourceId, ResourceTypeEnum resourceType, Integer permissionTypeCode, Long userId) {
        ResourcePermission permission = new ResourcePermission();
        // 设置教材ID
        permission.setResourceId(resourceId);
        // 设置教材类型为书籍
        permission.setResourceType(resourceType.getCode());
        // 设置教材阅读权限
        permission.setPermissionType(permissionTypeCode);
        // 设置教材协作者ID
        permission.setUserId(userId);
        return permission;
    }

    /**
     * 取得当前教材和当前章节需要更新的权限列表<br>
     * 1.如果仅有教材阅读权限，则删除该权限，否则保留该权限<br>
     * 2.判断用户是否在当前章节的协作者列表中，如果在，则保留该权限，否则删除该权限<br>
     *
     * @param bookId       : 教材ID
     * @param chapterId    : 章节ID
     * @param bookNewEditors : 新的教材协作者用户ID列表
     * @return : 需要更新的权限列表
     */
    private List<ResourcePermission> getNeedUpdatePermissions(String bookId, String chapterId, List<Long> bookNewEditors) {
        List<ResourcePermission> needUpdatePermissions = new ArrayList<>();

        // 取得教材所有章节
        List<ChapterPO> chapterPOS = chapterPOMapper.selectByBookId(bookId);
        // 取得所有章节ID(如果删除唯一章节后变更权限，则章节列表为空)
        Set<String> chapterIds = chapterPOS.stream().map(ChapterPO::getId).collect(Collectors.toSet());
        chapterIds.add(chapterId);

        // 取得教材阅读用户(协作者的阅读权限)
        PermissionSearchParam permissionSearchParam = new PermissionSearchParam(bookId, ResourceTypeEnum.BOOK, PermissionTypeEnum.READ);
        List<ResourcePermission> bookReadPermissions = resourcePermissionService.getPermissions(List.of(permissionSearchParam));
        if (CollectionUtils.isEmpty(bookReadPermissions)) {
            return needUpdatePermissions;
        }
        List<Long> bookReaders = bookReadPermissions.stream().map(ResourcePermission::getUserId).toList();

        // 取得所有章节协作者用户(协作者的编辑权限)
        List<PermissionSearchParam> chapterPermissionSearchParams = chapterIds.stream().map(chapterIdItem ->
                new PermissionSearchParam(chapterIdItem, ResourceTypeEnum.CHAPTER, PermissionTypeEnum.EDIT)).toList();
        List<ResourcePermission> chapterEditPermissions = resourcePermissionService.getPermissions(chapterPermissionSearchParams);
        if (CollectionUtils.isEmpty(chapterEditPermissions)) {
            chapterEditPermissions = Collections.emptyList();
        }
        // 统计当前教材每个协作者拥有编辑权限的章节数量
        Map<Long, Long> editorChaptersCountMap = chapterEditPermissions.stream()
                .collect(Collectors.groupingBy(ResourcePermission::getUserId, Collectors.counting()));

        // 更新当前章节协作者用户权限
        for (ResourcePermission chapterPermission : chapterEditPermissions) {
            String anyChapterId = chapterPermission.getResourceId();
            Long chapterEditor = chapterPermission.getUserId();
            Integer updateEditPermission;
            if (anyChapterId.equals(chapterId) && !bookNewEditors.contains(chapterEditor)) {
                // 不在当前章节最新协作者列表中，需要删除对应的编辑权限
                updateEditPermission = ~PermissionTypeEnum.EDIT.getCode();
                // 减少该用户编辑章节数量
                editorChaptersCountMap.computeIfPresent(chapterEditor, (k, v) -> v - 1);
            }else {
                // 保留存在的编辑权限
                updateEditPermission = PermissionTypeEnum.EDIT.getCode();
            }
            needUpdatePermissions.add(buildPermissionParam(anyChapterId, ResourceTypeEnum.CHAPTER, updateEditPermission, chapterEditor));
        }

        // 更新教材阅读权限(不要计算权限，直接赋值想要的权限，最终更新数据库的时候会统一合并处理)
        bookReaders.forEach(uid -> {
            Integer needUpdateReadPermission;
            if (editorChaptersCountMap.getOrDefault(uid, 0L)==0L) {
                // 删除协作者独立的教材阅读权限(没有任何章节编辑权限)
                needUpdateReadPermission = ~PermissionTypeEnum.READ.getCode();
            }else{
                // 保留已存在的阅读权限
                needUpdateReadPermission = PermissionTypeEnum.READ.getCode();
            }
            needUpdatePermissions.add(buildPermissionParam(bookId, ResourceTypeEnum.BOOK, needUpdateReadPermission, uid));
        });

        // 返回更新后的权限列表
        return needUpdatePermissions;
    }

}
