package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.BookVersionInfoVersionRelationPOMapper;
import com.unipus.digitalbook.dao.ComplementResourcePOMapper;
import com.unipus.digitalbook.dao.ComplementResourceReferencePOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.complement.ComplementResourceList;
import com.unipus.digitalbook.model.entity.complement.ComplementResourceReference;
import com.unipus.digitalbook.model.enums.ResultMessage;
import com.unipus.digitalbook.model.po.complement.ComplementResourcePO;
import com.unipus.digitalbook.model.po.complement.ComplementResourceReferencePO;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.ComplementResourceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 配套资源接口实现类
 */
@Service
@Slf4j
public class ComplementResourceServiceImpl implements ComplementResourceService {

    private static final String DEFAULT_VERSION_NUMBER = "0";

    @Resource
    private ComplementResourceReferencePOMapper complementResourceReferencePOMapper;

    @Resource
    private ComplementResourcePOMapper complementResourcePOMapper;

    @Resource
    private ChapterService chapterService;

    @Resource
    private BookVersionInfoVersionRelationPOMapper bookVersionInfoVersionRelationPOMapper;

    /**
     * 添加配套资源信息
     */
    @Override
    public String addResource(ComplementResource complementResource) {
        ComplementResourcePO complementResourcePO = new ComplementResourcePO(complementResource);
        complementResourcePO.setResourceId(IdentifierUtil.getShortUUID());
        complementResourcePOMapper.insertSelective(complementResourcePO);
        return complementResourcePO.getResourceId();
    }

    @Override
    public Long saveResource(ComplementResource complementResource) {
        ComplementResourcePO complementResourcePO = new ComplementResourcePO(complementResource);
        if (complementResourcePO.getResourceId() == null) {
            complementResourcePO.setResourceId(IdentifierUtil.getShortUUID());
        }
        complementResourcePOMapper.insertSelective(complementResourcePO);
        return complementResourcePO.getId();
    }

    /**
     * 根据主键ID获取资源信息
     *
     * @param id 主键ID
     * @return ComplementResource 对象，如果未找到则返回null
     */
    @Override
    public ComplementResource getResourceById(Long id) {
        // 根据主键ID查询数据库中的资源信息
        ComplementResourcePO complementResourcePO = complementResourcePOMapper.selectByPrimaryKey(id);
        // 如果查询结果为空，则直接返回null
        if (complementResourcePO == null) {
            return null;
        }
        // 将数据库实体对象转换为业务实体对象
        return complementResourcePO.toEntity();
    }


    /**
     * 更新配套资源信息
     */
    @Override
    public Boolean updateName(String resourceId, String name, Long opUserId) {
        ComplementResourcePO complementResourcePO = complementResourcePOMapper.selectByResourceId(resourceId);
        if (complementResourcePO == null) {
           throw new IllegalArgumentException("资源不存在");
        }
        ComplementResourcePO updateComplementResourcePO = new ComplementResourcePO();
        updateComplementResourcePO.setId(complementResourcePO.getId());
        updateComplementResourcePO.setName(name);
        updateComplementResourcePO.setUpdateBy(opUserId);
        return complementResourcePOMapper.updateByPrimaryKeySelective(updateComplementResourcePO) > 0;
    }

    /**
     * 查询配套资源信息
     */
    @Override
    public ComplementResourceList search(String bookId, Integer resourceType, Integer visibleStatus, PageParams pageParams) {
        Integer count = complementResourcePOMapper.count(bookId, resourceType, visibleStatus);
        if (count == null || count == 0) {
            return new ComplementResourceList(Collections.emptyList(), 0);
        }
        List<ComplementResource> resources = complementResourcePOMapper.search(bookId, resourceType, visibleStatus, pageParams).stream()
                .map(ComplementResourcePO::toEntity).toList();
        return new ComplementResourceList(resources, count);
    }

    @Override
    public Boolean delete(String resourceId, Long opUserId) {
        ComplementResourcePO complementResourcePO = complementResourcePOMapper.selectByResourceId(resourceId);
        if (complementResourcePO == null) {
            throw new IllegalArgumentException("资源不存在");
        }
        return complementResourcePOMapper.logicalDelete(complementResourcePO.getId(), opUserId) > 0;
    }

    @Override
    public Boolean updateVisibleStatus(String resourceId, Integer visibleStatus, Long opUserId) {
        ComplementResourcePO complementResourcePO = complementResourcePOMapper.selectByResourceId(resourceId);
        if (complementResourcePO == null) {
            throw new IllegalArgumentException("资源不存在");
        }
        return complementResourcePOMapper.updateVisibleStatus(complementResourcePO.getId(), visibleStatus, opUserId) > 0;
    }

    @Override
    public ComplementResourceList getResourceByBookId(String bookId) {
        List<ComplementResource> resources = complementResourcePOMapper.selectResourceByBookIdAndVersion(bookId, DEFAULT_VERSION_NUMBER).stream()
                .map(ComplementResourcePO::toEntity).toList();
        return new ComplementResourceList(resources, DEFAULT_VERSION_NUMBER).generateMd5();
    }

    @Override
    public ComplementResourceList generateVersionResource(String bookId) {
        String version = IdentifierUtil.generateVersion();
        complementResourcePOMapper.generateVersionResource(bookId, version);
        List<ComplementResource> resources = complementResourcePOMapper.selectResourceByBookIdAndVersion(bookId, version).stream()
                .map(ComplementResourcePO::toEntity).toList();
        return new ComplementResourceList(resources, version).generateMd5();
    }

    private boolean isUse(String resourceId) {
        ComplementResourceReferencePO complementResourceReference = new ComplementResourceReferencePO();
        complementResourceReference.setComplementResourceId(resourceId);
        List<ComplementResourceReferencePO> complementResourceReferences = complementResourceReferencePOMapper.selectReference(complementResourceReference);
        if (CollectionUtils.isEmpty(complementResourceReferences)) {
            return false;
        }
        Set<String> chapterIds = complementResourceReferences.stream().map(ComplementResourceReferencePO::getChapterId).collect(Collectors.toSet());
        return chapterService.existChapter(chapterIds);
    }
    /**
     * 添加媒体关系
     * @param bookMediaReference 教材与媒体引用实体参数
     * @return 添加成功返回引用ID，失败返回Null
     */
    @Override
    public Long addReference(ComplementResourceReference bookMediaReference, Long opUserId){
        ComplementResourceReferencePO complementResourceReferencePO = ComplementResourceReferencePO.build(bookMediaReference, opUserId);
        List<ComplementResourceReferencePO> complementResourceReferencePOList =
                complementResourceReferencePOMapper.selectReference(complementResourceReferencePO);
        if(!CollectionUtils.isEmpty(complementResourceReferencePOList)){
            log.warn("添加配套资源引用失败，该章节与配套资源引用已存在，chapterId:{},complementResourceId:{}",
                    bookMediaReference.getChapterId(), bookMediaReference.getComplementResourceId());
            throw new BizException(ResultMessage.BIZ_MEDIA_REFERENCE_ADD_FAIL, "该章节与配套资源引用已存在");
        }

        Integer count = complementResourceReferencePOMapper.insertReference(complementResourceReferencePO);
        return count>0 ? complementResourceReferencePO.getId(): null;
    }

    /**
     * 删除媒体关系
     * @param referenceId 教材与媒体引用ID
     * @param opUserId 操作人ID
     * @return 删除成功返回true，失败返回false
     */
    @Override
    public Boolean removeReference(Long referenceId, Long opUserId) {
        ComplementResourceReferencePO complementResourceReferencePO = new ComplementResourceReferencePO();
        complementResourceReferencePO.setId(referenceId);
        complementResourceReferencePO.setEnable(false);
        complementResourceReferencePO.setUpdateBy(opUserId);
        Integer count = complementResourceReferencePOMapper.updateReference(complementResourceReferencePO);
        return count>0;
    }

    /**
     * 更新媒体关系
     * @param bookMediaReference 教材与媒体引用实体参数
     * @param opUserId 操作人ID
     */
    @Override
    public Boolean updateReference(ComplementResourceReference bookMediaReference, Long opUserId) {
        ComplementResourceReferencePO complementResourceReferencePO = ComplementResourceReferencePO.build(bookMediaReference, opUserId);
        Integer count = complementResourceReferencePOMapper.updateReference(complementResourceReferencePO);
        return count>0;
    }

    /**
     * 查询配套资源引用列表
     * @param complementResourceReference 查询配套资源引用列表参数
     * @return 媒体关系列表
     */
    @Override
    public List<ComplementResourceReference> getReference(ComplementResourceReference complementResourceReference) {
        ComplementResourceReferencePO complementResourceReferencePO = ComplementResourceReferencePO.build(complementResourceReference, null);
        List<ComplementResourceReferencePO> complementResourceReferencePOList =
                complementResourceReferencePOMapper.selectReference(complementResourceReferencePO);
        if(CollectionUtils.isEmpty(complementResourceReferencePOList)){
            return Collections.emptyList();
        }
        return complementResourceReferencePOList.stream().map(ComplementResourceReferencePO::toEntity).toList();
    }

    /**
     * 根据教材版本ID,获取配套资源列表
     *
     * @param bookVersionId 教材版本ID
     * @return 配套资源列表
     */
    @Override
    public List<ComplementResource> getResourceByBookVersionId(Long bookVersionId) {
       List<ComplementResourcePO> complementResourcePOList =  complementResourcePOMapper.selectByBookVersionId(bookVersionId);
       if(!CollectionUtils.isEmpty(complementResourcePOList)){
           return complementResourcePOList.stream().map(ComplementResourcePO::toEntity).toList();
       }
        return List.of();
    }
}
