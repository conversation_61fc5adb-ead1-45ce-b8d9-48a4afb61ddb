package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.user.ThirdPartyUserException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.ThirdPartyUserInfoPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.producer.TenantMessageProducer;
import com.unipus.digitalbook.service.TenantSubscribeService;
import com.unipus.digitalbook.service.ThirdPartyUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 第三方用户服务实现类
 *
 * <AUTHOR>
 * @date 2025/6/23 18:21
 */
@Slf4j
@Service
public class ThirdPartyUserServiceImpl implements ThirdPartyUserService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ThirdPartyUserInfoPOMapper thirdPartyUserInfoPOMapper;

    @Resource
    private TenantSubscribeService tenantSubscribeService;

    @Resource
    private TenantMessageProducer tenantMessageProducer;

    @Override
    public ThirdPartyUserInfo getUserInfoByOpenId(Long tenantId, String openId) {
        if (tenantId == null || !StringUtils.hasText(openId)) {
            return null;
        }
        // 首先从本地数据库查询
        ThirdPartyUserInfoPO thirdPartyUserInfoPO = thirdPartyUserInfoPOMapper.selectByOpenIdAndTenantId(openId, tenantId);
        if (thirdPartyUserInfoPO != null) {
            return thirdPartyUserInfoPO.toEntity();
        }
        if (isTenantNotSubscribed(tenantId)) {
            log.info("tenantId: {} not subscribe messageTopic: {}", tenantId, MessageTopicEnum.THIRD_PARTY_USER_INFO.name());
        }
        // 本地查询不到，通过租户调用第三方用户服务获取用户信息
        ThirdPartyUserInfo thirdPartyUserInfo = getThirdPartyUserInfo(tenantId, openId);
        // 保存到本地数据库
        thirdPartyUserInfoPO = ThirdPartyUserInfoPO.fromEntity(thirdPartyUserInfo);
        thirdPartyUserInfoPOMapper.insertSelective(thirdPartyUserInfoPO);
        // 缓存用户信息到redis中
        stringRedisTemplate.opsForValue().set(CacheConstant.REDIS_THIRD_PARTY_USR_PREFIX + thirdPartyUserInfoPO.getId(),
                Objects.requireNonNull(JsonUtil.toJsonString(thirdPartyUserInfoPO)), CacheConstant.REDIS_USER_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        return thirdPartyUserInfoPO.toEntity();
    }

    /**
     * 判断租户是否订阅了第三方用户信息
     *
     * @param tenantId 租户ID
     * @return 是否订阅
     */
    private boolean isTenantNotSubscribed(Long tenantId) {
        TenantSubscribePO tenantSubscribePO = tenantSubscribeService.selectByTenantIdAndMessageTopic(tenantId,
                MessageTopicEnum.THIRD_PARTY_USER_INFO.name());
        return tenantSubscribePO == null;
    }

    /**
     * 通过租户调用第三方用户服务获取用户信息
     *
     * @param tenantId 租户ID
     * @param openId   用户OpenId
     * @return 第三方用户信息实体
     */
    private ThirdPartyUserInfo getThirdPartyUserInfo(Long tenantId, String openId) {
        if (tenantId == null || !StringUtils.hasText(openId)) {
            return null;
        }
        log.info("通过租户调用第三方用户服务获取用户信息，tenantId: {}, openId: {}", tenantId, openId);

        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                tenantId, MessageTopicEnum.THIRD_PARTY_USER_INFO)).isPresent()) {
            // 创建获取用户信息请求
            Map<String, String> params = new HashMap<>();
            params.put("tenantId", tenantId.toString());
            params.put("openId", openId);
            // 通过租户系统调用第三方服务
            Response<ThirdPartyUserInfo> response = tenantMessageProducer.produceSyncMessage(
                    tenantId,
                    MessageTopicEnum.THIRD_PARTY_USER_INFO,
                    new TypeReference<>() {
                    },
                    params,
                    new TypeReference<>() {
                    }
            );
            if (!response.isSuccess()) {
                throw new ThirdPartyUserException(response.getMessage());
            }
            ThirdPartyUserInfo thirdPartyUserInfo = response.getData();
            if (thirdPartyUserInfo == null) {
                log.error("第三方服务返回的用户信息为空，tenantId: {}, openId: {}", tenantId, openId);
                throw new ThirdPartyUserException("第三方服务返回的用户信息为空");
            }
            // 设置必要的字段
            thirdPartyUserInfo.setOpenId(openId);
            thirdPartyUserInfo.setTenantId(tenantId);
            return thirdPartyUserInfo;
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.THIRD_PARTY_USER_INFO.name(), tenantId);
            return null;
        }
    }

    /**
     * 根据第三方用户ID获取用户信息
     *
     * @param userId 第三方用户ID
     * @return 第三方用户信息实体
     */
    @Override
    public ThirdPartyUserInfo getUserInfoByUserId(Long userId) {
        // 先查询redis缓存
        String infojson = stringRedisTemplate.opsForValue().get(CacheConstant.REDIS_THIRD_PARTY_USR_PREFIX + userId);
        if (StringUtils.hasText(infojson)) {
            return JsonUtil.parseObject(infojson, ThirdPartyUserInfo.class);
        }
        ThirdPartyUserInfoPO thirdPartyUserInfoPO = thirdPartyUserInfoPOMapper.selectByPrimaryKey(userId);
        return thirdPartyUserInfoPO.toEntity();
    }

    @Override
    public String saveThirdPartyUser(ThirdPartyUserInfo thirdPartyUserInfo, Long userId) {
        ThirdPartyUserInfoPO thirdPartyUserInfoPO = new ThirdPartyUserInfoPO();
        if (Objects.isNull(thirdPartyUserInfo.getId())) {
            thirdPartyUserInfoPO.setCreateBy(userId);
            thirdPartyUserInfoPOMapper.insertSelective(thirdPartyUserInfoPO);
        } else {
            thirdPartyUserInfoPO.setUpdateBy(userId);
            thirdPartyUserInfoPOMapper.updateByPrimaryKeySelective(thirdPartyUserInfoPO);
        }
        log.info("保存第三方用户信息成功，{}", thirdPartyUserInfoPO);
        return thirdPartyUserInfoPO.getId().toString();
    }
}
