package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.content.*;
import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import com.unipus.digitalbook.model.entity.content.CopyContentItem;
import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.entity.content.PublishContentItem;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.model.params.content.*;
import com.unipus.digitalbook.service.CustomContentService;
import com.unipus.digitalbook.service.ThirdPartyUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("reader/customContent")
@Tag(name = "教材自建内容相关功能", description = "教材自建内容相关功能接口")
public class ReaderCustomContentController extends BaseController {

    @Resource
    private CustomContentService customContentService;
    @Resource
    private ThirdPartyUserService thirdPartyUserService;

    @PostMapping("/saveCustomContent")
    @Operation(summary = "保存编写中的自建内容信息", description = "保存编写中的自建内容信息")
    public Response<Boolean> saveCustomContent(@RequestBody CustomContentParam param) {
        CustomContent customContent = param.toEntity(getTenantId(), getThirdPartyUserId());
        Boolean result = customContentService.saveCustomContent(customContent, param.getContentPackage(), getDataPackage());
        return Response.success("保存自建内容信息成功", result);
    }

    @PostMapping("/saveBatchCustomContent")
    @Operation(summary = "批量保存编写中的自建内容信息", description = "批量保存编写中的自建内容信息")
    public Response<Boolean> saveBatchCustomContent(@RequestBody CustomContentListParam param) {
        Long userId = getThirdPartyUserId();
        // 构建CustomContent与contentPackage的映射
        Map<CustomContent, String> contentPackageMap = param.getCustomContentList().stream()
                .collect(Collectors.toMap(
                        customContentParam -> customContentParam.toEntity(getTenantId(), userId),
                        CustomContentParam::getContentPackage,
                        (existing, replacement) -> existing
                ));
        Boolean result = customContentService.saveBatchCustomContent(contentPackageMap, getDataPackage());
        return Response.success("批量保存自建内容信息成功", result);
    }

    @PostMapping("/updateCustomContentName")
    @Operation(summary = "更新编写中的自建内容名称", description = "更新编写中的自建内容名称")
    public Response<Boolean> updateCustomContentName(@RequestBody CustomContentNameParam param) {
        CustomContent customContent = param.toEntity(getTenantId(), getThirdPartyUserId());
        Boolean result = customContentService.updateCustomContentName(customContent, param.getContentPackage(), getDataPackage());
        return Response.success("更新自建内容名称成功", result);
    }

    @GetMapping("/getEditingCustomContentByBizId")
    @Operation(summary = "通过自建内容业务id获取编写中的自建内容信息", description = "通过自建内容业务id获取编写中的自建内容信息")
    public Response<CustomContentDTO> getEditingCustomContentByBizId(@RequestParam String bizId) {
        if (!ReaderTypeEnum.TEACHER.match(getReaderType())) {
            throw new BizException("学生用户不能获取编写中的自建内容信息");
        }
        Long tenantId = getTenantId();
        CustomContent customContent = customContentService.getEditingCustomContentByBizId(bizId, tenantId);
        bindQuestionToContent(customContent, tenantId);
        CustomContentDTO dto = new CustomContentDTO(customContent);
        return Response.success("获取自建内容信息成功", dto);
    }

    @PostMapping("/getEditingCustomContentByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取编写中的自建内容信息", description = "通过自建内容业务id批量获取编写中的自建内容信息")
    public Response<CustomContentListDTO> getEditingCustomContentByBizIds(@RequestBody CustomContentIdParam param) {
        if (!ReaderTypeEnum.TEACHER.match(getReaderType())) {
            throw new BizException("学生用户不能获取编写中的自建内容信息");
        }
        Long tenantId = getTenantId();
        List<CustomContent> customContentList = customContentService.getEditingCustomContentByBizIds(param.getBizIds(), tenantId);
        bindQuestionsToContentList(customContentList, tenantId);
        return Response.success(new CustomContentListDTO(customContentList));
    }

    @GetMapping("/getEditingCustomContentSimpleByBizId")
    @Operation(summary = "通过自建内容业务id获取编写中的自建内容简化信息", description = "通过自建内容业务id获取编写中的自建内容简化信息（不包含content、studentContent，包含questionList）")
    public Response<CustomContentSimpleDTO> getEditingCustomContentSimpleByBizId(@RequestParam String bizId) {
        if (!ReaderTypeEnum.TEACHER.match(getReaderType())) {
            throw new BizException("学生用户不能获取编写中的自建内容信息");
        }
        Long tenantId = getTenantId();
        CustomContent customContent = customContentService.getEditingCustomContentByBizId(bizId, tenantId);
        bindQuestionToContent(customContent, tenantId);
        CustomContentSimpleDTO dto = new CustomContentSimpleDTO(customContent);
        return Response.success("获取自建内容简化信息成功", dto);
    }

    @PostMapping("/getEditingCustomContentSimpleByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取编写中的自建内容简化信息", description = "通过自建内容业务id批量获取编写中的自建内容简化信息（不包含content、studentContent，包含questionList）")
    public Response<CustomContentSimpleListDTO> getEditingCustomContentSimpleByBizIds(@RequestBody CustomContentIdParam param) {
        if (!ReaderTypeEnum.TEACHER.match(getReaderType())) {
            throw new BizException("学生用户不能获取编写中的自建内容信息");
        }
        Long tenantId = getTenantId();
        List<CustomContent> customContentList = customContentService.getEditingCustomContentByBizIds(param.getBizIds(), tenantId);
        bindQuestionsToContentList(customContentList, tenantId);
        return Response.success(new CustomContentSimpleListDTO(customContentList));
    }

    @GetMapping("/getPublishedCustomContentByBizId")
    @Operation(summary = "通过自建内容业务id获取已发布的自建内容信息", description = "通过自建内容业务id获取已发布的自建内容信息")
    public Response<CustomContentDTO> getPublishedCustomContentByBizId(@RequestParam String bizId) {
        Long tenantId = getTenantId();
        CustomContent customContent = customContentService.getPublishedCustomContentByBizId(bizId, tenantId);
        bindQuestionToContent(customContent, tenantId);
        // 学生用户不返回内容详情
        if (!ReaderTypeEnum.TEACHER.match(getReaderType())) {
            customContent.setContent(null);
        }
        CustomContentDTO dto = new CustomContentDTO(customContent);
        return Response.success("获取自建内容信息成功", dto);
    }

    @PostMapping("/getPublishedCustomContentByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取已发布的自建内容信息", description = "通过自建内容业务id批量获取已发布的自建内容信息")
    public Response<CustomContentListDTO> getPublishedCustomContentByBizIds(@RequestBody CustomContentIdParam param) {
        Long tenantId = getTenantId();
        List<CustomContent> customContentList = customContentService.getPublishedCustomContentByBizIds(param.getBizIds(), tenantId);
        bindQuestionsToContentList(customContentList, tenantId);
        // 学生用户不返回内容详情
        if (!ReaderTypeEnum.TEACHER.match(getReaderType())) {
            customContentList.forEach(customContent -> customContent.setContent(null));
        }
        return Response.success(new CustomContentListDTO(customContentList));
    }

    @GetMapping("/getPublishedCustomContentSimpleByBizId")
    @Operation(summary = "通过自建内容业务id获取已发布的自建内容简化信息", description = "通过自建内容业务id获取已发布的自建内容简化信息（不包含content、studentContent，包含questionList）")
    public Response<CustomContentSimpleDTO> getPublishedCustomContentSimpleByBizId(@RequestParam String bizId) {
        Long tenantId = getTenantId();
        CustomContent customContent = customContentService.getPublishedCustomContentSimpleByBizId(bizId, tenantId);
        bindQuestionToContent(customContent, tenantId);
        CustomContentSimpleDTO dto = new CustomContentSimpleDTO(customContent);
        return Response.success("获取已发布自建内容简化信息成功", dto);
    }

    @PostMapping("/getPublishedCustomContentSimpleByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取已发布的自建内容简化信息", description = "通过自建内容业务id批量获取已发布的自建内容简化信息（不包含content、studentContent，包含questionList）")
    public Response<CustomContentSimpleListDTO> getPublishedCustomContentSimpleByBizIds(@RequestBody CustomContentIdParam param) {
        Long tenantId = getTenantId();
        List<CustomContent> customContentList = customContentService.getPublishedCustomContentSimpleByBizIds(param.getBizIds(), tenantId);
        bindQuestionsToContentList(customContentList, tenantId);
        return Response.success(new CustomContentSimpleListDTO(customContentList));
    }

    @PostMapping("/deleteCustomContentByBizIds")
    @Operation(summary = "通过自建内容业务id批量删除编写中的自建内容信息", description = "通过自建内容业务id批量删除编写中的自建内容信息")
    public Response<Boolean> deleteCustomContentByBizIds(@RequestBody CustomContentIdParam param) {
        Boolean result = customContentService.deleteCustomContentByBizIds(param.getBizIds(), getTenantId(), getThirdPartyUserId(), getDataPackage());
        return Response.success("批量删除自建内容信息成功", result);
    }

    @PostMapping("/publishCustomContent")
    @Operation(summary = "发布自建内容信息", description = "发布自建内容信息")
    public Response<Boolean> publishCustomContent(@RequestBody PublishCustomContentParam param) {
        // 转换param为entity列表
        List<PublishContentItem> publishContentList = param.getPublishItemList() != null ?
                param.getPublishItemList().stream().map(PublishContentItemParam::toEntity).toList() : null;
        List<PublishContentItem> deleteContentList = param.getDeleteItemList() != null ?
                param.getDeleteItemList().stream().map(PublishContentItemParam::toEntity).toList() : null;
        Boolean result = customContentService.publishCustomContent(publishContentList, deleteContentList, getTenantId(), getThirdPartyUserId(), getDataPackage());
        return Response.success("发布自建内容信息成功", result);
    }

    @PostMapping("/copyCustomContentByBizIds")
    @Operation(summary = "复制自建内容", description = "复制的自建内容为编写中状态")
    public Response<CopyCustomContentResultDTO> copyCustomContentByBizIds(@RequestBody CopyCustomContentParam param) {
        // 转换param为entity列表
        List<CopyContentItem> copyContentList = param.getItemList() != null ?
                param.getItemList().stream().map(CopyContentItemParam::toEntity).toList() : null;
        Map<String, String> result = customContentService.copyCustomContentByBizIds(copyContentList, param.getStatus(), getTenantId(), getThirdPartyUserId(), getDataPackage());
        return Response.success("复制自建内容成功", new CopyCustomContentResultDTO(result));
    }

    @PostMapping("/getEditingCustomContentCatalogByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取编写中的自建内容目录信息", description = "通过自建内容业务id批量获取编写中的自建内容目录信息")
    public Response<CustomContentCatalogListDTO> getEditingCustomContentCatalogByBizIds(@RequestBody CustomContentIdParam param) {
        if (!ReaderTypeEnum.TEACHER.match(getReaderType())) {
            throw new BizException("学生用户不能获取编写中的自建内容信息");
        }
        Long tenantId = getTenantId();
        List<CustomContent> customContentList = customContentService.getEditingCustomContentCatalogByBizIds(param.getBizIds(), tenantId);
        return Response.success(new CustomContentCatalogListDTO(customContentList));
    }

    @PostMapping("/getPublishedCustomContentCatalogByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取已发布的自建内容目录信息", description = "通过自建内容业务id批量获取已发布的自建内容目录信息")
    public Response<CustomContentCatalogListDTO> getPublishedCustomContentCatalogByBizIds(@RequestBody CustomContentIdParam param) {
        Long tenantId = getTenantId();
        List<CustomContent> customContentList = customContentService.getPublishedCustomContentCatalogByBizIds(param.getBizIds(), tenantId);
        return Response.success(new CustomContentCatalogListDTO(customContentList));
    }

    /**
     * 获取题目并绑定到单个内容对象
     *
     * @param customContent 自建内容对象
     * @param tenantId      租户ID
     */
    private void bindQuestionToContent(CustomContent customContent, Long tenantId) {
        List<BigQuestionGroup> questionList = customContentService.getQuestionListByContentId(customContent.getId(), tenantId);
        customContent.setQuestionList(questionList);
    }

    /**
     * 获取题目并绑定到内容对象列表
     *
     * @param customContentList 自建内容列表
     * @param tenantId          租户ID
     */
    private void bindQuestionsToContentList(List<CustomContent> customContentList, Long tenantId) {
        if (customContentList.isEmpty()) {
            return;
        }
        Map<Long, List<BigQuestionGroup>> questionMap = customContentService.getQuestionMapByContentIds(
                customContentList.stream().map(CustomContent::getId).toList(),
                tenantId
        );
        customContentList.forEach(customContent -> customContent.setQuestionList(questionMap.get(customContent.getId())));
    }

    /**
     * 通过getTenantId和getOpenId获取第三方用户id
     *
     * @return 第三方用户ID
     */
    private Long getThirdPartyUserId() {
        ThirdPartyUserInfo userInfo = thirdPartyUserService.getUserInfoByOpenId(getTenantId(), getOpenId());
        return Optional.ofNullable(userInfo).map(ThirdPartyUserInfo::getId).orElse(null);
    }
}
