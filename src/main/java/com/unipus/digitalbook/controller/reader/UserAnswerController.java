package com.unipus.digitalbook.controller.reader;


import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.common.utils.ScoreUtil;
import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.question.*;
import com.unipus.digitalbook.model.entity.clio.ClioSig;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.SubmitAnswerContext;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.params.question.AnswerScoreParam;
import com.unipus.digitalbook.model.params.question.GetClioSigParam;
import com.unipus.digitalbook.model.params.question.UserAnswerListParam;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.QuestionService;
import com.unipus.digitalbook.service.UserAnswerService;
import com.unipus.digitalbook.service.remote.restful.soe.SoeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 用户作答接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("reader/answer")
@Tag(name = "读者作答接口相关功能", description = "作答接口相关接口")
public class UserAnswerController extends BaseController {
    @Resource
    private UserAnswerService userAnswerService;
    @Resource
    private QuestionService questionService;
    @Resource
    private SoeService soeService;
    @Resource
    private ChapterService chapterService;

    @PostMapping("/judgeStart")
    @Operation(summary = "开始进行评测", description = "提交用户答案进行评测，只用于异步作答场景", method = "POST")
    public Response<JudgeTaskListDTO> judgeStart(@RequestBody AnswerScoreParam request) {
        String openId = getOpenId();
        // 获取大题
        BigQuestionGroup bigQuestion = userAnswerService.getBigQuestionForJudge(request.getGroupId(), request.getVersionNumber());
        UserAnswerList userAnswerList = request.toEntity(openId, getTenantId(), getEnvPartition());
        List<JudgeTaskDTO> judgeTaskList = userAnswerService.judgeStart(bigQuestion, userAnswerList)
                .stream().map(JudgeTaskDTO::new).toList();
        return Response.success(new JudgeTaskListDTO(judgeTaskList));
    }

    @PostMapping("/judgeResult")
    @Operation(summary = "获取评测结果", description = "获取评测结果，只用于异步作答场景 获取judgeStart的结果", method = "POST")
    public Response<UserAnswerResponseDTO> fetchJudgeResult(@RequestBody UserAnswerListParam request) {
        String openId = getOpenId();
        UserAnswerList userAnswerList = request.toEntity(openId);
        List<UserAnswerResultDTO> userAnswersResult = userAnswerService.fetchJudgeResult(userAnswerList).getUserAnswers()
                .stream().map(UserAnswerResultDTO::new).collect(Collectors.toList());
        return Response.success(new UserAnswerResponseDTO(userAnswersResult));
    }

    @PostMapping("/submitScore")
    @Operation(summary = "用户提交作答", description = "提交用户答案进行判分并保存用户作答记录，所有题型通用", method = "POST")
    public Response<UserAnswerResponseDTO> submitScore(@RequestBody AnswerScoreParam request) {
        String openId = getOpenId();
        // 构建题组
        String envPartition = getEnvPartition();
        Long tenantId = getTenantId();
        BigQuestionGroup bigQuestion = userAnswerService.getBigQuestionForJudge(request.getGroupId(), request.getVersionNumber());
        UserAnswerList userAnswerList = request.toEntity(openId, tenantId, envPartition);
        UserAnswerResponseDTO userAnswerResponseDTO = new UserAnswerResponseDTO();
        Long chapterVersionId = chapterService.getChapterVersionIdByChapterIdAndVersionNumber(request.getChapterId(), request.getChapterVersionNumber());
        SubmitAnswerContext context = request.toContext(tenantId, openId, envPartition, getClientIp(), getDataPackage(), chapterVersionId);
        UserAnswerList resultAnswerList = userAnswerService.submitScore(bigQuestion, userAnswerList, context);
        // 返回用户答题结果
        List<UserAnswerResultDTO> userAnswerResultList = resultAnswerList.getUserAnswers().stream().map(UserAnswerResultDTO::new).toList();
        // 用户百分制得分
        userAnswerResponseDTO.setScore(ScoreUtil.keepOneDecimalForPercent(bigQuestion.getScore(), resultAnswerList.getScore()));
        // 获取从题型里解析正确答案列表
        userAnswerResponseDTO.setCorrectAnswers(AnswerDTO.toDTOList(bigQuestion.fetchCorrectAnswers()));
        userAnswerResponseDTO.setUserAnswersResult(userAnswerResultList);
        userAnswerResponseDTO.setQuestionAnalysis(QuestionAnalysisDTO.toDTOList(bigQuestion.fetchQuestionAnalysis()));
        return Response.success(userAnswerResponseDTO);
    }

    @GetMapping("/list")
    @Operation(summary = "获取用户答题记录", description = "获取用户答题记录", method = "GET")
    public Response<UserAnswerResponseDTO> getUserAnswers(String questionId, String versionNumber) {
        BigQuestionGroup bigQuestion = questionService.getBigQuestion(questionId, versionNumber);
        Set<String> bizQuestionIds = bigQuestion.fetchCorrectAnswers().keySet();
        String openId = getOpenId();
        Long tenantId = getTenantId();
        // 查询所有版本的作答记录
        List<UserAnswer> userAnswers = userAnswerService.getLatestUserAnswers(bizQuestionIds, null, openId, tenantId);
        if (userAnswers.isEmpty()) {
            return Response.success(new UserAnswerResponseDTO());
        }
        UserAnswerResponseDTO userAnswerResponseDTO = new UserAnswerResponseDTO();
        userAnswerResponseDTO.setUserAnswersResult(userAnswers.stream().map(UserAnswerResultDTO::new).toList());
        userAnswerResponseDTO.setCorrectAnswers(AnswerDTO.toDTOList(bigQuestion.fetchCorrectAnswers()));
        BigDecimal userScore = userAnswers.stream().map(UserAnswer::getScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        userAnswerResponseDTO.setScore(ScoreUtil.keepOneDecimalForPercent(bigQuestion.getScore(), userScore));
        userAnswerResponseDTO.setQuestionAnalysis(QuestionAnalysisDTO.toDTOList(bigQuestion.fetchQuestionAnalysis()));
        return Response.success(userAnswerResponseDTO);
    }

    @PostMapping("/getClioSig")
    @Operation(summary = "获取clio签名配置", description = "获取clio签名配置", method = "POST")
    public Response<GetClioSigDTO> getClioSig(@RequestBody GetClioSigParam param) {
        // 根据用户id获取openId
        String openId = getOpenId();
        Long tenantId = getTenantId();

        // 生成clio请求tokenId
        String tokenId = UUID.randomUUID().toString().replace("-", "");
        // 获取clio配置
        ClioSig clioSig = soeService.getClioSig(tokenId, openId, param.getSoeQuestionType());
        String soeLimit = clioSig.getSoeLimit();
        // 缓存用户异步作答回调记录
        JSONObject callbackBody = new JSONObject();
        callbackBody.put("bizQuestionId", param.getBizQuestionId());
        callbackBody.put("questionType", QuestionTypeEnum.getCodeByName(param.getQuestionType()));
        callbackBody.put("tenantId", tenantId);
        callbackBody.put("openId", openId);
        callbackBody.put("soeLimit", soeLimit);
        userAnswerService.setAnswerCallback(tokenId, callbackBody.toJSONString());
        return Response.success(new GetClioSigDTO(clioSig));
    }

    @GetMapping("/getAnswerCallbackEvaluation")
    @Operation(summary = "获取用户评测结果", description = "根据作答业务ID获取用户评测结果", method = "GET")
    public Response<GetUserAnswerEvaluationDTO> getAnswerCallbackEvaluation(@RequestParam String bizAnswerId) {
        String evaluation = userAnswerService.getAnswerCallbackEvaluation(bizAnswerId);
        return Response.success(new GetUserAnswerEvaluationDTO(bizAnswerId, evaluation));
    }
}
