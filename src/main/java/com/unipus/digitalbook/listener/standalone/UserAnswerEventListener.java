package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.question.SubmitAnswerContext;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.events.UserAnswerEvent;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentActionFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class UserAnswerEventListener {
    @Resource
    private UserContentActionFactory userContentActionFactory;

    @EventListener
    public void handleUserAnswerEvent(UserAnswerEvent event) {
        SubmitAnswerContext context = event.getContext();
        String bizQuestionId = event.getQuestion().getBizGroupId();
        log.debug("收到用户作答事件: {}, {}, {}, {}", event.getQuestion().getId(), event.getQuestion().getId(), event.getEventType(), context.getContentType());
        List<UserAnswer> userAnswers = event.getUserAnswers();
        if (CollectionUtils.isEmpty(userAnswers)) {
            return;
        }
        UserAction userAction = new UserAction();
        userAction.setQuestionId(bizQuestionId);
        userAction.setTenantId(context.getTenantId());
        userAction.setOpenId(context.getOpenId());
        userAction.setIp(context.getClientIp());
        userAction.setContentId(context.getContentId());
        userAction.setContentVersionId(context.getContentVersionId());
        userAction.setDataPackage(context.getDataPackage());
        userAction.setContentType(context.getContentType());
        // 完成节点
        userContentActionFactory.getContentAction(context.getContentType()).finishQuestionNode(userAction);
    }
}
