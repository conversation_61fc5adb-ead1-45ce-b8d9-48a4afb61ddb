package com.unipus.digitalbook.common.exception.question;

/**
 * 题库返回值异常。
 */
public class GrpcQuestionInvalidResponseException extends RuntimeException {

    /**
     * 构造题库返回值异常。
     */
    public GrpcQuestionInvalidResponseException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造题库返回值异常。
     */
    public GrpcQuestionInvalidResponseException(String message) {
        super(message);
    }

}
