package com.unipus.digitalbook.common.exception.permission;

/**
 * 数据权限异常。
 */
public class PermissionCheckException extends RuntimeException {

    /**
     * 构造数据权限异常。
     */
    public PermissionCheckException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造数据权限异常。
     */
    public PermissionCheckException(String message) {
        super(message);
    }

}
