package com.unipus.digitalbook.common.exception.user;

/**
 * 用户登录信息异常异常。
 */
public class UserAuthInfoException extends RuntimeException {


    /**
     * 构造用户登录信息异常异常。
     */
    public UserAuthInfoException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造用户登录信息异常异常。
     */
    public UserAuthInfoException(String message) {
        super(message);
    }
}
