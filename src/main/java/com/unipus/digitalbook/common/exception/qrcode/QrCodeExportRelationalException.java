package com.unipus.digitalbook.common.exception.qrcode;

/**
 * <AUTHOR>
 * @date 2025/3/10 10:08
 */
public class QrCodeExportRelationalException extends RuntimeException {

    public QrCodeExportRelationalException() {
        super("导出二维码关系数据失败");
    }

    public QrCodeExportRelationalException(String message) {
        super(message);
    }

    public QrCodeExportRelationalException(String message, Throwable cause) {
        super(message, cause);
    }
}
