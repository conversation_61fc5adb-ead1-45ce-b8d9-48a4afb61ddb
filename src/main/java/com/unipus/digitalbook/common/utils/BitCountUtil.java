package com.unipus.digitalbook.common.utils;

public class BitCountUtil {
    private static final int[] BIT_COUNTS = new int[256];

    static {
        for (int i = 0; i < 256; i++) {
            BIT_COUNTS[i] = Integer.bitCount(i);
        }
    }
    private BitCountUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * 获取整个 byte[] 中 1 的总数量
     * @param data byte 数组
     * @return 总共的 1 的数量（完成数）
     */
    public static int countBitsIn(byte[] data) {
        if (data == null) return 0;
        int count = 0;
        for (byte b : data) {
            count += BIT_COUNTS[b & 0xFF];
        }
        return count;
    }
    /**
     * 获取进度信息（完成数量和总数量）
     * @param data byte 数组
     * @return int[0] = 完成数量（1 的个数），int[1] = 总题数（bit 位数）
     */
    public static int[] getProgressStat(byte[] data) {
        if (data == null) return new int[]{0, 0};
        int finished = countBitsIn(data);
        int total = data.length * 8;
        return new int[]{finished, total};
    }
}
