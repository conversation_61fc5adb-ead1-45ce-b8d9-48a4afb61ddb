package com.unipus.digitalbook.common.utils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class LockUtil {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 分布式锁
     *
     * @param key        锁key
     * @param timeoutSec 锁等待时间，单位s
     * @param runner     同步执行逻辑
     * @return 是否执行了同步内容
     */
    public boolean tryLock(String key, long timeoutSec, Runnable runner) {
        RLock lock = redissonClient.getLock(key);
        try {
            boolean success = false;
            try {
                success = lock.tryLock(timeoutSec, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.warn("同步锁等待时被中断，key：{}", key);
            }
            if (success) {
                log.info("同步锁获取成功，key：{}", key);
                runner.run();
                return true;
            }
            log.info("同步锁获取失败，key：{}", key);
            return false;
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("同步锁释放成功，key：{}", key);
            }
        }
    }
}
