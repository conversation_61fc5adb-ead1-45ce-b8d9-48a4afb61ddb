package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.business.BloomFilterException;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Component
public class BloomFilterUtil {
    private static final Logger log = LoggerFactory.getLogger(BloomFilterUtil.class);

    // 定义默认的误判概率，即在查询不存在于集合中的元素时，返回错误结果的概率为0.001
    private static final double DEFAULT_FALSE_PROBABILITY = 0.001;
    // 定义布隆过滤器预期插入的数据量，默认值为100,000条记录
    private static final long DEFAULT_EXPECTED_INSERTIONS = 100000;
    // 设置最小预期插入数量
    private static final long MIN_EXPECTED_INSERTIONS = 100;


    private final RedissonClient redissonClient;

    @Autowired
    public BloomFilterUtil(RedissonClient redissonClient) {
        this.redissonClient = Objects.requireNonNull(redissonClient, "RedissonClient must not be null");
    }

    /**
     * 初始化布隆过滤器（使用默认参数）
     *
     * @param filterName 过滤器名称
     * @return 是否初始化成功
     */
    public boolean initBloomFilter(String filterName) {
        return initBloomFilter(filterName, DEFAULT_EXPECTED_INSERTIONS, DEFAULT_FALSE_PROBABILITY);
    }

    /**
     * 初始化布隆过滤器
     *
     * @param filterName 过滤器名称
     * @param expectedInsertions 预计插入的元素数量
     * @param falseProbability 误判率
     * @return 是否初始化成功
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    public boolean initBloomFilter(String filterName, long expectedInsertions, double falseProbability) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);
        validateParameters(expectedInsertions, falseProbability);

        try {
            RBloomFilter<Object> bloomFilter = redissonClient.getBloomFilter(filterName);
            boolean result = bloomFilter.tryInit(expectedInsertions, falseProbability);
            log.info("Bloom filter '{}' initialization {}: expectedInsertions={}, falseProbability={}",
                    filterName, result ? "succeeded" : "failed", expectedInsertions, falseProbability);
            return result;
        } catch (Exception e) {
            throw new BloomFilterException("Failed to initialize bloom filter", e);
        }
    }

    /**
     * 添加元素到布隆过滤器
     *
     * @param filterName 过滤器名称
     * @param element 元素
     * @return 是否添加成功
     */
    public boolean addElement(String filterName, Object element) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);
        Objects.requireNonNull(element, "Element must not be null");

        try {
            RBloomFilter<Object> bloomFilter = getBloomFilter(filterName);
            boolean result = bloomFilter.add(element);
            log.debug("Added element to bloom filter '{}': {}", filterName, element);
            return result;
        } catch (Exception e) {
            throw new BloomFilterException("Failed to add element", e);
        }
    }



    /**
     * 批量添加元素
     *
     * @param filterName 过滤器名称
     * @param elements 元素集合
     * @return 添加的元素数量
     */
    public long addElements(String filterName, Iterable<?> elements) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);
        Objects.requireNonNull(elements, "Elements must not be null");

        try {
            RBloomFilter<Object> bloomFilter = getBloomFilter(filterName);
            long count = 0;
            for (Object element : elements) {
                if (element != null && bloomFilter.add(element)) {
                    count++;
                }
            }
            log.info("Added {} elements to bloom filter '{}'", count, filterName);
            return count;
        } catch (Exception e) {
            throw new BloomFilterException("Failed to add elements", e);
        }
    }

    /**
     * 检查元素是否在布隆过滤器中
     *
     * @param filterName 过滤器名称
     * @param element 元素
     * @return 布尔值，表示元素是否存在
     */
    public boolean containsElement(String filterName, Object element) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);
        Objects.requireNonNull(element, "Element must not be null");

        try {
            RBloomFilter<Object> bloomFilter = getBloomFilter(filterName);
            return bloomFilter.contains(element);
        } catch (Exception e) {
            throw new BloomFilterException("Failed to check element", e);
        }
    }

    /**
     * 设置过滤器的过期时间
     *
     * @param filterName 过滤器名称
     * @param timeout 过期时间
     * @return 是否设置成功
     */
    public boolean expire(String filterName, long timeout) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);
        try {
            RBloomFilter<Object> bloomFilter = getBloomFilter(filterName);
            return bloomFilter.expire(Duration.ofSeconds(timeout));
        } catch (Exception e) {
            throw new BloomFilterException("Failed to set expiration", e);
        }
    }

    /**
     * 删除布隆过滤器
     *
     * @param filterName 过滤器名称
     * @return 是否删除成功
     */
    public boolean deleteBloomFilter(String filterName) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);

        try {
            RBloomFilter<Object> bloomFilter = redissonClient.getBloomFilter(filterName);
            boolean result = bloomFilter.delete();
            log.info("Deleted bloom filter '{}': {}", filterName, result);
            return result;
        } catch (Exception e) {
            throw new BloomFilterException("Failed to delete bloom filter", e);
        }
    }

    /**
     * 获取布隆过滤器的统计信息
     *
     * @param filterName 过滤器名称
     * @return 布隆过滤器的统计信息
     */
    public BloomFilterStats getBloomFilterStats(String filterName) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);

        try {
            RBloomFilter<Object> bloomFilter = getBloomFilter(filterName);
            return new BloomFilterStats(
                    bloomFilter.getExpectedInsertions(),
                    bloomFilter.getFalseProbability(),
                    bloomFilter.count(),
                    bloomFilter.getSize()
            );
        } catch (Exception e) {
            throw new BloomFilterException("Failed to get bloom filter stats", e);
        }
    }


    /**
     * 构建布隆过滤器
     *
     * @param filterName 过滤器名称
     * @param dataSupplier 数据供应源
     * @param expectedInsertions 预期插入量
     * @param falseProbability 误判率
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 构建结果
     */
    public BuildResult buildBloomFilter(
            String filterName,
            Supplier<Collection<?>> dataSupplier,
            long expectedInsertions,
            double falseProbability,
            long expireTime,
            TimeUnit timeUnit) {

        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);
        Objects.requireNonNull(dataSupplier, "DataSupplier must not be null");
        Objects.requireNonNull(timeUnit, "TimeUnit must not be null");

        try {
            log.info("Starting to build bloom filter: {}", filterName);

            // 1. 获取数据
            Collection<?> data = dataSupplier.get();
            if (data == null || data.isEmpty()) {
                log.warn("No data provided for bloom filter: {}", filterName);
                return new BuildResult(filterName, 0, BuildStatus.NO_DATA);
            }

            // 2. 计算实际的预期插入量
            long actualExpectedInsertions = Math.max(expectedInsertions, data.size());

            // 3. 创建新的过滤器（使用临时名称）
            String tempFilterName = filterName + "_temp_" + System.currentTimeMillis();
            RBloomFilter<Object> newFilter = redissonClient.getBloomFilter(tempFilterName);
            newFilter.tryInit(actualExpectedInsertions, falseProbability);

            // 4. 添加数据
            long addedCount = 0;
            for (Object item : data) {
                if (item != null && newFilter.add(item)) {
                    addedCount++;
                }
            }

            // 5. 设置过期时间
            if (expireTime > 0) {
                Duration duration = Duration.of(expireTime, timeUnit.toChronoUnit());
                newFilter.expire(duration);
            }

            // 6. 原子性地替换旧过滤器
            RBloomFilter<Object> oldFilter = redissonClient.getBloomFilter(filterName);
            boolean exists = oldFilter.isExists();

            // 重命名临时过滤器
            if (exists) {
                oldFilter.delete();
            }
            redissonClient.getBloomFilter(tempFilterName).rename(filterName);

            log.info("Successfully built bloom filter '{}': added {} items", filterName, addedCount);
            return new BuildResult(filterName, addedCount, BuildStatus.SUCCESS);

        } catch (Exception e) {
            throw new BloomFilterException("Failed to build bloom filter", e);
        }
    }

    /**
     * 重构布隆过滤器
     *
     * @param filterName 过滤器名称
     * @param dataSupplier 新数据供应源
     * @return 重构结果
     */
    public RebuildResult rebuildBloomFilter(String filterName, Supplier<Collection<?>> dataSupplier) {
        validateFilterName(filterName);
        filterName = cal4Cluster(filterName);
        Objects.requireNonNull(dataSupplier, "DataSupplier must not be null");

        try {
            log.info("Starting to rebuild bloom filter: {}", filterName);

            // 1. 获取原过滤器的配置
            RBloomFilter<Object> oldFilter = redissonClient.getBloomFilter(filterName);
            if (!oldFilter.isExists()) {
                log.warn("Original bloom filter does not exist: {}", filterName);
                return new RebuildResult(filterName, 0, 0, RebuildStatus.NOT_FOUND);
            }

            BloomFilterStats oldStats = getBloomFilterStats(filterName);

            // 2. 构建新的过滤器
            BuildResult buildResult = buildBloomFilter(
                    filterName,
                    dataSupplier,
                    oldStats.expectedInsertions(),
                    oldStats.falseProbability(),
                    -1, // 临时不设置过期时间
                    TimeUnit.SECONDS
            );

            if (buildResult.status() != BuildStatus.SUCCESS) {
                return new RebuildResult(filterName, oldStats.count(), 0, RebuildStatus.BUILD_FAILED);
            }

            log.info("Successfully rebuilt bloom filter '{}': old count={}, new count={}",
                    filterName, oldStats.count(), buildResult.addedCount());

            return new RebuildResult(
                    filterName,
                    oldStats.count(),
                    buildResult.addedCount(),
                    RebuildStatus.SUCCESS
            );

        } catch (Exception e) {
            throw new BloomFilterException("Failed to rebuild bloom filter", e);
        }
    }

    /**
     * 获取布隆过滤器实例
     *
     * @param filterName 过滤器名称
     * @return 布隆过滤器实例
     * @throws BloomFilterException 如果过滤器不存在
     */
    private RBloomFilter<Object> getBloomFilter(String filterName) {
        RBloomFilter<Object> bloomFilter = redissonClient.getBloomFilter(filterName);
        if (!bloomFilter.isExists()) {
            throw new BloomFilterException("Bloom filter does not exist: " + filterName);
        }
        return bloomFilter;
    }

    public boolean isExists(String filterName) {
        RBloomFilter<Object> bloomFilter = redissonClient.getBloomFilter(filterName);
        return bloomFilter.isExists();
    }

    private void validateFilterName(String filterName) {
        if (!StringUtils.hasText(filterName)) {
            throw new IllegalArgumentException("Filter name must not be empty");
        }
    }

    private String cal4Cluster(String filterName){
        return "{"+filterName+"}";
    }

    private void validateParameters(long expectedInsertions, double falseProbability) {
        if (expectedInsertions < MIN_EXPECTED_INSERTIONS) {
            throw new IllegalArgumentException("ExpectedInsertions must be >= " + MIN_EXPECTED_INSERTIONS);
        }
        if (falseProbability <= 0 || falseProbability >= 1) {
            throw new IllegalArgumentException("FalseProbability must be between 0 and 1");
        }
    }

    /**
         * 布隆过滤器的统计信息类
         */
        public record BloomFilterStats(long expectedInsertions, double falseProbability, long count, long size) {
    }


    /**
     * 构建结果状态枚举
     */
    public enum BuildStatus {
        SUCCESS,
        NO_DATA,
        FAILED
    }

    /**
     * 重构结果状态枚举
     */
    public enum RebuildStatus {
        SUCCESS,
        NOT_FOUND,
        BUILD_FAILED,
        FAILED
    }

    /**
         * 构建结果类
         */
        public record BuildResult(String filterName, long addedCount, BuildStatus status) {
        }

    /**
         * 重构结果类
         */
        public record RebuildResult(String filterName, long oldCount, long newCount, RebuildStatus status) {
        }

}