package com.unipus.digitalbook.common.utils;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class Base62 {
    // 定义Base62字符集，按照0-9, a-z, A-Z的顺序
    private static final String ALPHABET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int BASE = ALPHABET.length();
    private static final int[] CHAR_TO_INDEX = new int[128];

    static {
        for (int i = 0; i < ALPHABET.length(); i++) {
            CHAR_TO_INDEX[ALPHABET.charAt(i)] = i;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(Base62.class);

    /**
     * 将十进制数转换为Base62编码
     *
     * @param num 需要编码的十进制数
     * @return Base62编码的字符串
     */
    public static String encode(long num) {
        if (num == 0) {
            return String.valueOf(ALPHABET.charAt(0));
        }

        StringBuilder sb = new StringBuilder();
        while (num > 0) {
            int remainder = (int) (num % BASE);
            sb.append(ALPHABET.charAt(remainder));
            num = num / BASE;
        }
        return sb.reverse().toString();
    }

    /**
     * 将Base62编码转换为十进制数
     *
     * @param str Base62编码的字符串
     * @return 解码后的十进制数
     */
    public static long decode(String str) {
        long result = 0;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            result = result * BASE + CHAR_TO_INDEX[c];
        }

        return result;
    }


    /**
     * 将十进制数转换为Base62编码
     * @param num 需要编码的十进制数
     * @return Base62编码的字符串
     */
    public static String encodeO(long num) {
        if (num == 0) {
            return String.valueOf(ALPHABET.charAt(0));
        }

        StringBuilder sb = new StringBuilder();
        while (num > 0) {
            int remainder = (int) (num % BASE);
            sb.insert(0, ALPHABET.charAt(remainder));
            num = num / BASE;
        }

        return sb.toString();
    }

    /**
     * 将Base62编码转换为十进制数
     * @param str Base62编码的字符串
     * @return 解码后的十进制数
     */
    public static long decodeO(String str) {
        long result = 0;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            result = result * BASE + ALPHABET.indexOf(c);
        }

        return result;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 编码测试
        long[] testValues = {0, 10, 62, 100, 1000, 999999, 12935, 12302524982393812L, 7878812785789823932L};
        int outerLoop = 10_000_000;
        int innerLoop = testValues.length;
        long totalOps = (long) outerLoop * innerLoop;

        System.out.println("==================== Base62 性能对比测试 ====================");
        System.out.printf("测试数据量：%d 次（外层循环 %d × 内层测试值 %d）%n", totalOps, outerLoop, innerLoop);
        System.out.println("------------------------------------------------------------");

        Date start = new Date();
        for (int i = 0; i < outerLoop; i++) {
            for (long val : testValues) {
                String encoded = encodeO(val);
                long decoded = decodeO(encoded);
            }
        }
        Date end = new Date();
        long duration1 = end.getTime() - start.getTime();
        double qps1 = totalOps / (duration1 / 1000.0);

        System.out.println("[原始实现] encodeO/decodeO 总耗时: " + duration1 + " ms");
        System.out.printf("[原始实现] QPS: %.2f ops/sec%n", qps1);
        System.out.println("------------------------------------------------------------");

        Date start2 = new Date();
        for (int i = 0; i < outerLoop; i++) {
            for (long val : testValues) {
                String encoded = encode(val);
                long decoded = decode(encoded);
            }
        }
        Date end2 = new Date();
        long duration2 = end2.getTime() - start2.getTime();
        double qps2 = totalOps / (duration2 / 1000.0);

        System.out.println("[优化实现] encode/decode 总耗时: " + duration2 + " ms");
        System.out.printf("[优化实现] QPS: %.2f ops/sec%n", qps2);
        System.out.println("------------------------------------------------------------");

        double speedup = duration1 > 0 ? (double) duration1 / duration2 : 0;
        System.out.printf("性能提升倍数（优化/原始）: %.2f ×%n", speedup);
        System.out.println("==================== 环境信息 ====================");
        System.out.println("Java 版本: " + System.getProperty("java.version"));
        System.out.println("Java 供应商: " + System.getProperty("java.vendor"));
        System.out.println("Java Home: " + System.getProperty("java.home"));
        System.out.println("操作系统: " + System.getProperty("os.name") + " " + System.getProperty("os.version") + " (" + System.getProperty("os.arch") + ")");
        System.out.println("当前用户: " + System.getProperty("user.name"));
        System.out.println("==================================================");
    }
}
