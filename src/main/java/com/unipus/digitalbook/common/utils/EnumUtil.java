package com.unipus.digitalbook.common.utils;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

public class EnumUtil {

    // 获取枚举值对应的位标志
    public static <T extends Enum<T>> int getBit(T bit) {
        return 1 << bit.ordinal();
    }

    // 开启多个枚举值对应的位标志
    @SafeVarargs
    public static <T extends Enum<T>> int on(int bit, T... ts) {
        for (T t : ts) {
            bit |= getBit(t);
        }
        return bit;
    }

    // 关闭多个枚举值对应的位标志
    @SafeVarargs
    public static <T extends Enum<T>> int off(int bit, T... ts) {
        for (T t : ts) {
            bit &= ~getBit(t);
        }
        return bit;
    }

    // 检查某个枚举值是否开启
    public static <T extends Enum<T>> boolean isOn(int bit, T t) {
        return (bit & getBit(t)) != 0;
    }

    // 检查多个枚举值是否全部开启
    @SafeVarargs
    public static <T extends Enum<T>> boolean and(int bit, T... ts) {
        int mask = or(ts);
        return (bit & mask) == mask;
    }

    // 检查是否至少有一个枚举值开启
    @SafeVarargs
    public static <T extends Enum<T>> boolean or(int bit, T... ts) {
        int mask = or(ts);
        return (bit & mask) != 0;
    }

    // 获取多个枚举值的位标志
    @SafeVarargs
    public static <T extends Enum<T>> int or(T... ts) {
        int value = 0;
        for (T t : ts) {
            value |= getBit(t);
        }
        return value;
    }

    // 获取当前位标志对应的所有枚举值
    public static <T extends Enum<T>> List<T> elements(int bit, Class<T> cls) {
        T[] enumConstants = cls.getEnumConstants();
        List<T> result = Lists.newArrayList();
        for (T t : enumConstants) {
            if (isOn(bit, t)) {
                result.add(t);
            }
        }
        return result;
    }

    // 获取当前位标志对应的所有枚举值的位标志
    public static <T extends Enum<T>> List<Integer> bits(int bit, Class<T> cls) {
        return elements(bit, cls).stream()
                .map(EnumUtil::getBit)
                .collect(Collectors.toList());
    }

    // 获取当前位标志对应的所有枚举值的序号
    public static <T extends Enum<T>> List<Integer> ordinals(int bit, Class<T> cls) {
        return elements(bit, cls).stream()
                .map(Enum::ordinal)
                .collect(Collectors.toList());
    }

    // 获取当前值对应的所有可能的枚举值组合
    public static <T extends Enum<T>> List<Integer> values(int bit, Class<T> cls) {
        T[] enumConstants = cls.getEnumConstants();
        int max = 1 << enumConstants.length; // 最大可能的组合数
        List<Integer> result = Lists.newArrayList();
        for (int i = 0; i < max; i++) {
            if ((bit & i) == i) {
                result.add(i);
            }
        }
        return result;
    }

    // 开启多个整数位标志
    public static int on(int bit, int... bs) {
        for (int b : bs) {
            bit |= b;
        }
        return bit;
    }

    // 关闭多个整数位标志
    public static int off(int bit, int... bs) {
        for (int b : bs) {
            bit &= ~b;
        }
        return bit;
    }

    // 检查某个整数位标志是否开启
    public static boolean isOn(int bit, int b) {
        return (bit & b) != 0;
    }

    // 检查多个整数位标志是否全部开启
    public static boolean and(int bit, int... bs) {
        int mask = or(bs);
        return (bit & mask) == mask;
    }

    // 检查是否至少有一个整数位标志开启
    public static boolean or(int bit, int... bs) {
        int mask = or(bs);
        return (bit & mask) != 0;
    }

    // 获取多个整数位标志的或运算结果
    public static int or(int... bs) {
        int value = 0;
        for (int b : bs) {
            value |= b;
        }
        return value;
    }
}
