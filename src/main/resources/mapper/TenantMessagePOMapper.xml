<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.TenantMessagePOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.tenant.TenantMessagePO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="messageTopic" column="message_topic" jdbcType="VARCHAR"/>
        <result property="message" column="message" jdbcType="VARCHAR"/>
        <result property="messageUuid" column="message_uuid" jdbcType="VARCHAR"/>
        <result property="async" column="async" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <resultMap id="IdResultMap" type="java.lang.Long">
        <id property="id" column="id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,message_topic,message,message_uuid,async,
        create_time,update_time,create_by,update_by,enable
    </sql>

    <insert id="insertMessage" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.tenant.TenantMessagePO" useGeneratedKeys="true">
        insert into tenant_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="messageTopic != null">message_topic,</if>
            <if test="message != null">message,</if>
            <if test="messageUuid != null">message_uuid,</if>
            <if test="async != null">async,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="tenantId != null">#{tenantId,jdbcType=BIGINT},</if>
            <if test="messageTopic != null">#{messageTopic,jdbcType=VARCHAR},</if>
            <if test="message != null">#{message,jdbcType=VARCHAR},</if>
            <if test="messageUuid != null">#{messageUuid,jdbcType=VARCHAR},</if>
            <if test="async != null">#{async,jdbcType=BIT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tenant_message
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByTenantIdAndMessageTopicLimitFromId" resultType="com.unipus.digitalbook.model.po.tenant.TenantMessagePO" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tenant_message
        where tenant_id = #{tenantId,jdbcType=BIGINT} and id > #{id,jdbcType=BIGINT}
        and message_topic = #{messageTopic,jdbcType=VARCHAR} and enable = true and async = true
        order by id limit #{limit,jdbcType=INTEGER}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tenant_message
        where id = #{id,jdbcType=BIGINT}
    </delete>

</mapper>
