<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.QuestionGroupPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.QuestionGroupPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="bizGroupId" column="biz_group_id" jdbcType="CHAR"/>
            <result property="versionNumber" column="version_number" jdbcType="CHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="direction" column="direction" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="analysis" column="analysis" jdbcType="VARCHAR"/>
            <result property="questionText" column="question_text" jdbcType="VARCHAR"/>
            <result property="difficulty" column="difficulty" jdbcType="INTEGER"/>
            <result property="score" column="score" jdbcType="DECIMAL"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,biz_group_id,
        version_number,type,direction,
        content,analysis,question_text,
        difficulty,score, sort_order,create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question_group
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from question_group
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <update id="deleteByIds">
        update
        question_group
        set enable = false and update_by = #{opsUserId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.QuestionGroupPO" useGeneratedKeys="true">
        insert into question_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="bizGroupId != null">biz_group_id,</if>
            <if test="versionNumber != null">version_number,</if>
            <if test="type != null">type,</if>
            <if test="direction != null">direction,</if>
            <if test="content != null">content,</if>
            <if test="analysis != null">analysis,</if>
            <if test="questionText != null">question_text,</if>
            <if test="difficulty != null">difficulty,</if>
            <if test="score != null">score,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="parentId != null">#{parentId,jdbcType=BIGINT},</if>
            <if test="bizGroupId != null">#{bizGroupId,jdbcType=CHAR},</if>
            <if test="versionNumber != null">#{versionNumber,jdbcType=CHAR},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            <if test="direction != null">#{direction,jdbcType=VARCHAR},</if>
            <if test="content != null">#{content,jdbcType=VARCHAR},</if>
            <if test="analysis != null">#{analysis,jdbcType=VARCHAR},</if>
            <if test="questionText != null">#{questionText,jdbcType=VARCHAR},</if>
            <if test="difficulty != null">#{difficulty,jdbcType=INTEGER},</if>
            <if test="score != null">#{score,jdbcType=DECIMAL},</if>
            <if test="sortOrder != null">#{sortOrder,jdbcType=DECIMAL},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">
            id = LAST_INSERT_ID(id),
            <if test="parentId != null">parent_id = VALUES(parent_id),</if>
            <if test="type != null">type = VALUES(type),</if>
            <if test="direction != null"> direction = VALUES(direction),</if>
            <if test="content != null"> content = VALUES(content),</if>
            <if test="analysis != null">analysis = VALUES(analysis),</if>
            <if test="questionText != null">question_text = VALUES(question_text),</if>
            <if test="difficulty != null">difficulty = VALUES(difficulty),</if>
            <if test="score != null">score = VALUES(score),</if>
            <if test="sortOrder != null">sort_order = VALUES(sort_order),</if>
            <if test="updateBy != null">update_by = VALUES(update_by),</if>
            <if test="enable != null">enable = VALUES(enable),</if>
            update_time = NOW(),
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.QuestionGroupPO">
        update question_group
        <set>
                <if test="parentId != null">
                    parent_id = #{parentId,jdbcType=BIGINT},
                </if>
                <if test="bizGroupId != null">
                    biz_group_id = #{bizGroupId,jdbcType=CHAR},
                </if>
                <if test="versionNumber != null">
                    version_number = #{versionNumber,jdbcType=CHAR},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="direction != null">
                    direction = #{direction,jdbcType=VARCHAR},
                </if>
                <if test="content != null">
                    content = #{content,jdbcType=VARCHAR},
                </if>
                <if test="analysis != null">
                    analysis = #{analysis,jdbcType=VARCHAR},
                </if>
                <if test="questionText != null">
                    question_text = #{questionText,jdbcType=VARCHAR},
                </if>
                <if test="difficulty != null">
                    difficulty = #{difficulty,jdbcType=INTEGER},
                </if>
                <if test="score != null">
                    score = #{score,jdbcType=DECIMAL},
                </if>
                <if test="sortOrder != null">
                    sort_order = #{sortOrder,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.question.QuestionGroupPO">
        update question_group
        set 
            parent_id =  #{parentId,jdbcType=BIGINT},
            biz_group_id =  #{bizGroupId,jdbcType=CHAR},
            version_number =  #{versionNumber,jdbcType=CHAR},
            type =  #{type,jdbcType=INTEGER},
            direction =  #{direction,jdbcType=VARCHAR},
            content =  #{content,jdbcType=VARCHAR},
            analysis =  #{analysis,jdbcType=VARCHAR},
            question_text =  #{questionText,jdbcType=VARCHAR},
            difficulty =  #{difficulty,jdbcType=INTEGER},
            score =  #{score,jdbcType=DECIMAL},
            sort_order =  #{sortOrder,jdbcType=INTEGER},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            enable =  #{enable,jdbcType=BIT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <!-- 通过题组ID查询默认版本题组对象 -->
    <select id="selectQuestionGroupByBizGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question_group
        where enable = true
            AND biz_group_id = #{bizGroupId}
        <if test="versionNumber != null">
            AND version_number = #{versionNumber}
        </if>
    </select>

    <sql id="Child_Column_List_With_Prefix">
        child.id, child.parent_id, child.biz_group_id, child.version_number,
        child.type, child.direction, child.content, child.analysis,
        child.question_text, child.difficulty, child.score, child.sort_order,
        child.create_time, child.update_time, child.create_by, child.update_by,
        child.enable
    </sql>

    <!-- 通过题组业务ID查询下级题库列表 -->
    <select id="selectChildByParentBizGroupId" resultMap="BaseResultMap">
        SELECT <include refid="Child_Column_List_With_Prefix" />
        FROM question_group child
        JOIN question_group parent ON parent.id = child.parent_id
        WHERE parent.enable = true
            AND parent.biz_group_id = #{parentBizGroupId}
            AND parent.version_number = #{versionNumber}
            AND child.enable = true
            AND child.version_number = #{versionNumber}
        ORDER BY child.create_time
    </select>

    <!-- 通过试卷模板ID查询下级题库信息及题库下题目信息 -->
    <select id="selectBankChildByPaperId" resultType="com.unipus.digitalbook.model.po.paper.QuestionBankPO">
        SELECT
            qg.id AS id,
            qg.biz_group_id AS bankId,
            qg.question_text AS bankName,
            qg.create_time AS createTime,
            qg.create_by AS createBy,
            qg.version_number AS versionNumber,
            (SELECT COUNT(1) FROM question_group q WHERE q.parent_id = qg.id AND q.enable = true AND q.version_number = #{versionNumber}) AS questionCount,
            qbs.question_score AS questionScore,
            qbs.questions_per_round AS questionCountPerRound
        FROM question_group qg
            JOIN question_bank_strategy qbs ON qg.biz_group_id = qbs.bank_id AND qbs.enable = true AND qbs.version_number = #{versionNumber}
        WHERE
            qg.enable = true
          <if test="paperId != null">
            AND qg.parent_id = (SELECT id FROM question_group WHERE biz_group_id = #{paperId} AND enable = true AND version_number = #{versionNumber} LIMIT 1 )
          </if>
          <if test="bankId != null">
            AND qg.biz_group_id = #{bankId}
          </if>
          AND qg.type = #{groupType,jdbcType=INTEGER}
          AND qg.version_number = #{versionNumber}
        ORDER BY qg.create_time DESC
    </select>

    <select id="selectLatestVersionByBizGroupId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
            FROM
                question_group
            WHERE
                enable = true AND biz_group_id = #{bizGroupId}
            ORDER BY version_number DESC
            LIMIT 1;
    </select>
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
            FROM
                question_group
            WHERE
                enable = true AND parent_id = #{parentId};
    </select>
    <select id="selectQuestionByParentId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            question_group
        WHERE
            enable = true AND parent_id in (SELECT id FROM question_group WHERE enable = true AND parent_id = #{parentId});
    </select>
    <select id="selectByBizGroupIdAndVersionNumber"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
            FROM
                question_group
            WHERE
                enable = true
            AND biz_group_id = #{bizGroupId}
            AND version_number = #{versionNumber}
    </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
            FROM
                question_group
            WHERE
                enable = true
            AND id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
    </select>
    <select id="selectIdByBizParentIdAndVersionNumberAndBizGroupId" resultType="java.lang.Long">
        SELECT id
        FROM question_group
        WHERE parent_id = (
            SELECT id
            FROM question_group
            WHERE biz_group_id = #{bizParentId, jdbcType=CHAR}
              AND version_number = #{parentVersionNumber, jdbcType=CHAR}
              AND enable = true
        )
        AND biz_group_id = #{bizGroupId, jdbcType=CHAR} AND enable = true
    </select>

    <!-- 通过题组业务ID取得上级题组信息 -->
    <select id="selectParentByChildBizGroupId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM question_group
        WHERE id = (
            SELECT parent_id
            FROM question_group
            WHERE biz_group_id = #{childBizGroupId}
                AND version_number = #{versionNumber}
        );
    </select>

</mapper>
