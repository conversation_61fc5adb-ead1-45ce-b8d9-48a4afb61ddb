package com.unipus.digitalbook.service;
import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.model.common.Response;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.StringUtils;

import java.util.concurrent.CompletableFuture;

@Slf4j
@SpringBootTest
public class TenantMessageProducerTest {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    // @Autowired
    // private TenantMessageProducer tenantMessageProducer;

    @Test
    @SneakyThrows
    public void testProducer() {
        try {
            kafkaTemplate.send("dev-ipublish-tenantMessageDelete", "key", "13")
                    .exceptionally(e -> {
                        log.error("exceptionally error" + e.getMessage());
                        return null;
                    });
            log.info("success");
        } catch (Exception e) {
            log.error("try catch error" + e.getMessage());
        }
    }

    @Test
    public void testProducer2() {
        System.out.println("main ThreadId:" + Thread.currentThread().threadId());
        CompletableFuture<String> future = CompletableFuture.failedFuture(new Exception("hello"));
        future.exceptionally(ex -> {
            System.out.println("ex1 ThreadId:" + Thread.currentThread().threadId());
            log.error("Exception occurred: " + ex.getMessage());
            throw new RuntimeException(ex);
        }).exceptionally(ex -> {
            System.out.println("ex2 ThreadId:" + Thread.currentThread().threadId());
            log.error("Exception occurred: " + ex.getMessage());
            return null;
        }).thenAccept(result -> {
            System.out.println("result ThreadId:" + Thread.currentThread().threadId());
            if (result == null) {
                log.info("No result due to exception");
            } else {
                log.info("Result: " + result);
            }
        }).join();
        System.out.println("Test completed");
    }

    @Test
    public void testProducer3() {
        System.out.println("main ThreadId:" + Thread.currentThread().threadId());
        CompletableFuture<String> future = CompletableFuture.completedFuture("hello");
        future.exceptionally(ex -> {
            System.out.println("ex1 ThreadId:" + Thread.currentThread().threadId());
            log.error("Exception occurred: " + ex.getMessage());
            throw new RuntimeException(ex);
        }).exceptionally(ex -> {
            System.out.println("ex2 ThreadId:" + Thread.currentThread().threadId());
            log.error("Exception occurred: " + ex.getMessage());
            return null;
        }).thenAccept(result -> {
            System.out.println("result ThreadId:" + Thread.currentThread().threadId());
            if (result == null) {
                log.info("No result due to exception");
            } else {
                log.info("Result: " + result);
            }
        }).join();
        System.out.println("Test completed");
    }

    @Test
    public void testProducer4() {
        System.out.println("main ThreadId:" + Thread.currentThread().threadId());
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            System.out.println("async ThreadId:" + Thread.currentThread().threadId());
            throw new RuntimeException("hello");
        });
        future.exceptionally(ex -> {
            System.out.println("ex1 ThreadId:" + Thread.currentThread().threadId());
            log.error("Exception occurred: " + ex.getMessage());
            throw new RuntimeException(ex);
        }).exceptionally(ex -> {
            System.out.println("ex2 ThreadId:" + Thread.currentThread().threadId());
            log.error("Exception occurred: " + ex.getMessage());
            return null;
        }).thenAccept(result -> {
            System.out.println("result ThreadId:" + Thread.currentThread().threadId());
            if (result == null) {
                log.info("No result due to exception");
            } else {
                log.info("Result: " + result);
            }
        }).join();
        System.out.println("Test completed");
    }

    // @Test
    // public void testProducer5() {
    //     CompletableFuture<Response<String>> future = tenantMessageProducer
    //             .produceAsyncMessage(1L, MessageTopicEnum.PUSH_USER_ANSWER,
    //             new TypeReference<>() {},
    //             new UserAnswerNodeData(),
    //             new TypeReference<>() {});
    //     future.exceptionally(ex -> {
    //         System.out.println("ex1 ThreadId:" + Thread.currentThread().threadId());
    //         log.error("Exception occurred: " + ex.getMessage());
    //         throw new RuntimeException(ex);
    //     }).exceptionally(ex -> {
    //         System.out.println("ex2 ThreadId:" + Thread.currentThread().threadId());
    //         log.error("Exception occurred: " + ex.getMessage());
    //         return null;
    //     }).thenAccept(result -> {
    //         System.out.println("result ThreadId:" + Thread.currentThread().threadId());
    //         if (result == null) {
    //             log.info("No result due to exception");
    //         } else {
    //             log.info("Result: " + result);
    //         }
    //     }).join();
    //     System.out.println("Test completed");
    // }

    @Test
    public void testProducer6() {
        TypeReference<String> stringType = new TypeReference<>() {};
        TypeReference<Response<String>> responseType = new TypeReference<>() {};
        System.out.println(stringType.getRawType().getTypeName().equals(Response.class.getTypeName()));
        System.out.println(responseType.getRawType().getTypeName().equals(Response.class.getTypeName()));
        System.out.println(stringType.getRawType().isAssignableFrom(Response.class));
        System.out.println(responseType.getRawType().isAssignableFrom(Response.class));
        System.out.println("abc");
    }

    @Test
    public void test() {
        System.out.println(String.join("/", "a", "b", "c"));
        System.out.println(StringUtils.hasText(" "));
        System.out.println(StringUtil.isBlank(" "));
    }

}
