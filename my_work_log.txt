2025-07-24 - refactor(paper): 重构试卷实例缓存管理
2025-07-24 - refactor(paper): 优化试卷实例缓存结构和序列化效率
2025-07-23 - feat(aop): 优化请求日志记录功能

2025-07-22 - feat(dev-token): 增强开发Token生成接口的安全性和可用性
2025-07-22 - feat(auth): 添加开发测试环境专用Token接口并更新安全配置

2025-07-21 - refactor(digitalbook): 优化数据结构和代码质量
2025-07-21 - refactor(digitalbook): 优化数据结构和代码质量
2025-07-21 - refactor(auth): 移除AuthController中的冗余代码

2025-07-17 - refactor(aop): 优化内部IP地址校验逻辑
- 修改未配置内部IP网段时的日志警告信息
- 更新IP地址校验通过时的日志信息

2025-07-17 - refactor: 移除getCurrentUserInfo方法的异常声明

2025-07-17 - refactor(mybatis): 优化字段类型映射
2025-07-17 - refactor(mapper): 优化PaperInstanceRelationPO映射配置
- 将paper_id和paper_version_number字段的JDBC类型从VARCHAR改为CHAR
- 这种修改可以提高数据库查询性能，因为CHAR类型的处理速度通常比VARCHAR快
2025-07-17 - fix:过滤本地回环网络IP地址

2025-07-16 - fix(ip): 优化IP地址获取和验证逻辑

2025-07-16 - feat(aop/log): 自定义请求和响应体缓存
2025-07-16 - refactor(log): 优化IP地址校验日志信息
- 修改了InternalIpInterceptor类中的日志信息，使其更加准确和易懂
- 在IpAddressUtil类中添加了调试输出，用于打印处理的IP列表
2025-07-16 - refactor(utils): 优化IP地址获取逻辑
2025-07-15 - refactor(aop/log): 重构请求日志记录功能

2025-07-15 - refactor(log): 优化请求日志过滤器的响应处理

2025-07-15 - refactor(aop): 移除TableChangeInterceptor中未使用的依赖
- 删除了未使用的ObjectMapper和@Resource注解
- 优化了代码结构，提高了代码的可读性和维护性
2025-07-15 - refactor(table-change): 重构表变更事件参数类型
2025-07-11 - refactor(digitalbook): 重构诊断测试成绩计算逻辑
2025-07-11 - refactor(digitalbook): 更新消息主题并增加新类型
2025-07-11 - refactor(digitalbook): 优化试卷成绩记录列表构建逻辑
2025-07-11 - refactor(digitalbook): 优化主观题处理逻辑和性能
2025-07-10 - refactor(paper): 重构试卷作答处理逻辑
2025-07-10 - refactor(question): 重构题目推送逻辑
2025-07-10 - refactor(question): 重构题目推送逻辑
2025-07-08 - refactor(paper): 重构试卷提交逻辑
2025-07-08 - refactor(mapper): 修改BookVersionChapterVersionRelationPOMapper查询语句
2025-07-08 - refactor(dao): 新增查询接口并优化上架检测逻辑
- 在BookVersionChapterVersionRelationPOMapper中新增selectChapterVersionByBookVersionId方法
- 该方法用于查询书的版本下所有章节的关系列表，并检查最新章节状态
- 修改PublishServiceImpl中的上架检测逻辑，使用新的查询方法
- 优化了对试卷引用关系的处理，仅保留最新引用存在的试卷的历史上架信息
2025-07-04 - refactor(digitalbook): 更新挑战统计信息查询接口
2025-07-04 - fix(paper): 优化小题判断逻辑
2025-07-04 - refactor(service): 优化题库相关功能和日志描述
2025-07-03 - refactor: 优化日志打印参数
2025-07-03 - refactor(paper): 重构BankQueryParam类
2025-07-03 - refactor(d paper): 优化诊断试卷相关方法参数
2025-07-03 - refactor(question-bank): 优化题库查询接口

2025-07-03 - refactor(diagnostic): 优化诊断测验答案策略

2025-07-03 - refactor(paper): 移除试卷提交扩展信息参数
2025-07-03 - refactor(paper): 查询成绩及作答记录时去除试卷版本条件
2025-07-03 - refactor(paper): 查询成绩及作答记录时去除试卷版本条件
2025-07-02 - refactor(paper): 优化试卷答案提交逻辑
2025-07-02 - refactor(paper): 优化成绩批量查询逻辑
2025-07-02 - fix(mapper): 为多个mapper文件添加LAST_INSERT_ID更新语句
2025-07-02 - fix(mapper): 修复问卷组重复插入问题

2025-07-02 - refactor(digitalbook): 优化PaperReferenceServiceImpl中的错误信息
- 将分号(;)替换为中文分号(；)以适应中文环境
- 修改错误信息的语气，使用逗号代替句号，使语气更缓和
2025-07-02 - refactor(digitalbook): 优化保存失败时的错误提示信息
- 修改了错误提示信息的格式，增加了"保存失败"的前缀
- 将被引用的试卷名称之间的逗号分隔符改为分号，提高可读性
2025-07-02 - refactor(digitalbook): 优化保存失败时的错误提示信息
- 修改了错误提示信息的格式，增加了"保存失败"的前缀
- 将被引用的试卷名称之间的逗号分隔符改为分号，提高可读性

2025-07-02 - fix(digitalbook): 优化章节删除和试卷引用逻辑
2025-07-02 - refactor(paper): 重构试卷引用处理逻辑
2025-07-01 - feat(service): 添加章节版本添加方法的事务控制
2025-07-01 - refactor(dao): 重构试卷引用保存逻辑
2025-07-02 - refactor(paper): 重构试卷引用处理逻辑
2025-07-01 - feat(service): 添加章节版本添加方法的事务控制
2025-07-01 - refactor(dao): 重构试卷引用保存逻辑